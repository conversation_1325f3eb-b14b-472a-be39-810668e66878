/**
 * Script de test pour vérifier que les webhooks de commandes créent bien les entrées en base
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testOrderCreation() {
  console.log('🧪 Test de création de commande via webhook...');
  
  try {
    // Simuler les données d'un webhook de commande Shopify
    const mockOrderData = {
      id: "test-order-123",
      customer: {
        id: "test-customer-456",
        first_name: "<PERSON>",
        last_name: "<PERSON><PERSON>",
        email: "<EMAIL>",
        total_spent: "150.00",
        orders_count: 2
      },
      total_price: "75.50",
      financial_status: "paid",
      fulfillment_status: "pending"
    };

    const shop = "test-shop.myshopify.com";

    // Importer les fonctions de webhook (utiliser import dynamique pour TypeScript)
    const { handleOrderCreated } = await import('../app/webhooks/points.server.ts');
    
    // Exécuter le webhook
    await handleOrderCreated(
      "ORDERS_CREATE",
      shop,
      JSON.stringify(mockOrderData)
    );

    // Vérifier que la commande a été créée
    const createdOrder = await prisma.order.findFirst({
      where: {
        orderId: mockOrderData.id,
        shop: shop
      },
      include: {
        customer: true
      }
    });

    if (createdOrder) {
      console.log('✅ Commande créée avec succès !');
      console.log(`   - ID: ${createdOrder.orderId}`);
      console.log(`   - Total: ${createdOrder.total}€`);
      console.log(`   - Client: ${createdOrder.customer.firstName} ${createdOrder.customer.lastName}`);
      console.log(`   - Statut: ${createdOrder.status}`);
      console.log(`   - Statut paiement: ${createdOrder.paymentStatus}`);
    } else {
      console.log('❌ Aucune commande trouvée en base de données');
    }

    // Vérifier que le client a été créé/mis à jour
    const customer = await prisma.customer.findFirst({
      where: {
        customerId: mockOrderData.customer.id,
        shop: shop
      }
    });

    if (customer) {
      console.log('✅ Client synchronisé avec succès !');
      console.log(`   - Points: ${customer.points}`);
      console.log(`   - Total dépensé: ${customer.totalSpent}€`);
      console.log(`   - Nombre de commandes: ${customer.ordersCount}`);
    } else {
      console.log('❌ Aucun client trouvé en base de données');
    }

    // Vérifier l'historique des points
    const pointsHistory = await prisma.pointsHistory.findMany({
      where: {
        metadata: {
          contains: mockOrderData.id
        }
      }
    });

    if (pointsHistory.length > 0) {
      console.log('✅ Historique des points créé !');
      pointsHistory.forEach(entry => {
        console.log(`   - Action: ${entry.action}, Points: ${entry.points}`);
      });
    } else {
      console.log('⚠️  Aucun historique de points trouvé (normal si aucune règle de points configurée)');
    }

  } catch (error) {
    console.error('❌ Erreur lors du test:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Fonction de nettoyage pour supprimer les données de test
async function cleanup() {
  console.log('🧹 Nettoyage des données de test...');
  
  try {
    // Supprimer les données de test
    await prisma.pointsHistory.deleteMany({
      where: {
        metadata: {
          contains: "test-order-123"
        }
      }
    });

    await prisma.order.deleteMany({
      where: {
        orderId: "test-order-123"
      }
    });

    await prisma.customer.deleteMany({
      where: {
        customerId: "test-customer-456"
      }
    });

    console.log('✅ Nettoyage terminé');
  } catch (error) {
    console.error('❌ Erreur lors du nettoyage:', error);
  }
}

// Exécuter le test
async function main() {
  await cleanup(); // Nettoyer d'abord au cas où
  await testOrderCreation();
  
  // Demander si on veut nettoyer
  const readline = require('readline');
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });

  rl.question('Voulez-vous nettoyer les données de test ? (y/N): ', async (answer) => {
    if (answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes') {
      await cleanup();
    }
    rl.close();
    process.exit(0);
  });
}

if (require.main === module) {
  main();
}
