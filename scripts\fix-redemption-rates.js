/**
 * <PERSON>ript pour corriger les taux de conversion
 */

import { PrismaClient } from '@prisma/client';
const prisma = new PrismaClient();

async function fixRedemptionRates() {
  try {
    console.log('🔧 Correction des taux de conversion...');

    // Mettre à jour tous les settings avec les bons taux
    const result = await prisma.settings.updateMany({
      data: {
        redemptionRate: 100.0,  // 100 points = 1€
        earningRate: 1.0,       // 1 point par euro dépensé
        minimumPoints: 100      // Minimum 100 points
      }
    });

    console.log(`✅ ${result.count} shops mis à jour avec les nouveaux taux:`);
    console.log('   - Taux de conversion: 100 points = 1€');
    console.log('   - Taux de gain: 1 point par euro dépensé');
    console.log('   - Points minimum: 100');

    // Vérifier les nouveaux paramètres
    const settings = await prisma.settings.findMany({
      select: {
        shop: true,
        redemptionRate: true,
        earningRate: true,
        minimumPoints: true
      }
    });

    console.log('\n📊 Paramètres mis à jour:');
    settings.forEach(setting => {
      console.log(`   ${setting.shop}:`);
      console.log(`     - Conversion: ${setting.redemptionRate} points/€`);
      console.log(`     - Gain: ${setting.earningRate} points/€`);
      console.log(`     - Minimum: ${setting.minimumPoints} points`);
    });

    // Test de calcul
    console.log('\n🧮 Tests de calcul:');
    const testCases = [
      { euros: 1, description: '1€' },
      { euros: 5, description: '5€' },
      { euros: 10, description: '10€' },
      { euros: 25, description: '25€' }
    ];

    testCases.forEach(test => {
      const pointsNeeded = test.euros * 100; // 100 points par euro
      const pointsEarned = test.euros * 1;   // 1 point par euro dépensé
      console.log(`   ${test.description}: ${pointsNeeded} points requis, ${pointsEarned} points gagnés`);
    });

  } catch (error) {
    console.error('❌ Erreur lors de la correction:', error);
  } finally {
    await prisma.$disconnect();
  }
}

fixRedemptionRates().catch(console.error);
