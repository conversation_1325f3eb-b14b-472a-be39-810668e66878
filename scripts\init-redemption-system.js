/**
 * Script pour initialiser le système de redemption avec des données de test
 */

import { PrismaClient } from '@prisma/client';
const prisma = new PrismaClient();

async function initRedemptionSystem() {
  try {
    console.log('🚀 Initialisation du système de redemption...');

    // Récupérer tous les shops
    const shops = await prisma.settings.findMany({
      select: { shop: true }
    });

    if (shops.length === 0) {
      console.log('❌ Aucun shop trouvé. Veuillez d\'abord configurer un shop.');
      return;
    }

    for (const { shop } of shops) {
      console.log(`\n📦 Configuration du shop: ${shop}`);

      // Vérifier si des ways to redeem existent déjà
      const existingWays = await prisma.wayToRedeem.findMany({
        where: { shop }
      });

      if (existingWays.length > 0) {
        console.log(`✅ ${existingWays.length} ways to redeem déjà configurées pour ${shop}`);
        continue;
      }

      // C<PERSON>er les ways to redeem par défaut
      const defaultWays = [
        {
          shop,
          name: "Coupon configurable",
          description: "Échangez vos points contre un coupon de réduction personnalisé",
          redeemType: "coupon",
          redeemValue: 0,
          pointsCost: 0,
          icon: "discount",
          isActive: true,
          isConfigurable: true,
          minPoints: 100,
          maxPoints: 10000,
          minValue: 1,
          maxValue: 100,
          expiryDays: 30,
          usageLimit: 1
        },
        {
          shop,
          name: "Coupon 5€",
          description: "Coupon de réduction de 5€ sur votre prochaine commande",
          redeemType: "coupon",
          redeemValue: 5,
          pointsCost: 500,
          icon: "discount",
          isActive: true,
          isConfigurable: false,
          expiryDays: 30,
          usageLimit: 1
        },
        {
          shop,
          name: "Coupon 10€",
          description: "Coupon de réduction de 10€ sur votre prochaine commande",
          redeemType: "coupon",
          redeemValue: 10,
          pointsCost: 1000,
          icon: "discount",
          isActive: true,
          isConfigurable: false,
          expiryDays: 30,
          usageLimit: 1
        },
        {
          shop,
          name: "Livraison gratuite",
          description: "Livraison gratuite sur votre prochaine commande",
          redeemType: "shipping",
          redeemValue: 5,
          pointsCost: 300,
          icon: "shipping",
          isActive: true,
          isConfigurable: false,
          expiryDays: 30,
          usageLimit: 1
        }
      ];

      // Insérer les ways to redeem
      const createdWays = await prisma.wayToRedeem.createMany({
        data: defaultWays
      });

      console.log(`✅ ${createdWays.count} ways to redeem créées pour ${shop}`);

      // Vérifier les paramètres de points
      const pointsSettings = await prisma.settings.findUnique({
        where: { shop },
        select: {
          redemptionRate: true,
          earningRate: true,
          minimumPoints: true
        }
      });

      if (pointsSettings) {
        console.log(`📊 Paramètres de points pour ${shop}:`);
        console.log(`   - Taux de conversion: ${pointsSettings.redemptionRate} points/€`);
        console.log(`   - Taux de gain: ${pointsSettings.earningRate} points/€`);
        console.log(`   - Points minimum: ${pointsSettings.minimumPoints}`);
      }
    }

    console.log('\n🎉 Système de redemption initialisé avec succès !');
    console.log('\n📝 Prochaines étapes:');
    console.log('1. Vérifiez l\'interface admin pour configurer les ways to redeem');
    console.log('2. Testez le widget client pour voir les options de redemption');
    console.log('3. Créez un client de test avec des points pour tester les échanges');

  } catch (error) {
    console.error('❌ Erreur lors de l\'initialisation:', error);
  } finally {
    await prisma.$disconnect();
  }
}

async function createTestCustomer() {
  try {
    console.log('\n👤 Création d\'un client de test...');

    const shops = await prisma.settings.findMany({
      select: { shop: true }
    });

    if (shops.length === 0) {
      console.log('❌ Aucun shop trouvé.');
      return;
    }

    const shop = shops[0].shop;

    // Créer ou mettre à jour un client de test
    const testCustomer = await prisma.customer.upsert({
      where: {
        customerId_shop: {
          customerId: "test-customer-123",
          shop: shop
        }
      },
      update: {
        points: 2000,
        type: "member"
      },
      create: {
        customerId: "test-customer-123",
        shop: shop,
        email: "<EMAIL>",
        firstName: "Test",
        lastName: "Customer",
        points: 2000,
        type: "member",
        ordersCount: 5
      }
    });

    console.log(`✅ Client de test créé: ${testCustomer.email} avec ${testCustomer.points} points`);

  } catch (error) {
    console.error('❌ Erreur lors de la création du client de test:', error);
  }
}

// Exécuter le script
async function main() {
  await initRedemptionSystem();
  await createTestCustomer();
}

main().catch(console.error);

export { initRedemptionSystem, createTestCustomer };
