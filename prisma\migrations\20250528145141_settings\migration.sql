/*
  Warnings:

  - Added the required column `updatedAt` to the `Settings` table without a default value. This is not possible if the table is not empty.

*/
-- CreateTable
CREATE TABLE "SettingsHistory" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "shop" TEXT NOT NULL,
    "changes" TEXT NOT NULL,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedBy" TEXT,
    CONSTRAINT "SettingsHistory_shop_fkey" FOREIGN KEY ("shop") REFERENCES "Settings" ("shop") ON DELETE RESTRICT ON UPDATE CASCADE
);

-- RedefineTables
PRAGMA defer_foreign_keys=ON;
PRAGMA foreign_keys=OFF;
CREATE TABLE "new_Settings" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "shop" TEXT NOT NULL,
    "earningRate" REAL NOT NULL DEFAULT 1.0,
    "redemptionRate" REAL NOT NULL DEFAULT 0.01,
    "minimumPoints" INTEGER NOT NULL DEFAULT 100,
    "expirationDays" INTEGER NOT NULL DEFAULT 365,
    "referralPoints" INTEGER NOT NULL DEFAULT 100,
    "birthdayPoints" INTEGER NOT NULL DEFAULT 250,
    "widgetEnabled" BOOLEAN NOT NULL DEFAULT true,
    "primaryColor" TEXT NOT NULL DEFAULT '#000000',
    "language" TEXT NOT NULL DEFAULT 'fr',
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL
);
INSERT INTO "new_Settings" ("birthdayPoints", "earningRate", "id", "language", "minimumPoints", "primaryColor", "redemptionRate", "referralPoints", "shop", "widgetEnabled") SELECT "birthdayPoints", "earningRate", "id", "language", "minimumPoints", "primaryColor", "redemptionRate", "referralPoints", "shop", "widgetEnabled" FROM "Settings";
DROP TABLE "Settings";
ALTER TABLE "new_Settings" RENAME TO "Settings";
CREATE UNIQUE INDEX "Settings_shop_key" ON "Settings"("shop");
PRAGMA foreign_keys=ON;
PRAGMA defer_foreign_keys=OFF;

-- CreateIndex
CREATE INDEX "SettingsHistory_shop_idx" ON "SettingsHistory"("shop");

-- CreateIndex
CREATE INDEX "SettingsHistory_createdAt_idx" ON "SettingsHistory"("createdAt");
