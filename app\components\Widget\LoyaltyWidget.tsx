import { useState, useEffect } from "react";
import { Card, Button, Text, Icon } from "@shopify/polaris";
import { CheckCircleIcon } from "@shopify/polaris-icons";
import { useTranslation } from "react-i18next";

interface LoyaltyWidgetProps {
  points: number;
  isVip?: boolean;
  onRedeem?: () => void;
  shop?: string;
  customerId?: string;
}

interface ProgramInfo {
  widgetColor?: string;
  widgetSecondaryColor?: string;
  widgetTextColor?: string;
  pointsName?: string;
  name?: string;
  description?: string;
}

export function LoyaltyWidget({ points, isVip = false, onRedeem, shop, customerId }: LoyaltyWidgetProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [programInfo, setProgramInfo] = useState<ProgramInfo | null>(null);
  const { t } = useTranslation();

  // Récupérer les paramètres du programme
  useEffect(() => {
    if (shop) {
      fetch(`/api/proxy?prepath=program-info&shop=${shop}`)
        .then(response => response.json())
        .then(data => {
          setProgramInfo(data);

          // Appliquer les styles dynamiques
          if (data.widgetColor) {
            document.documentElement.style.setProperty('--loyalty-primary', data.widgetColor);
          }
          if (data.widgetSecondaryColor) {
            document.documentElement.style.setProperty('--loyalty-secondary', data.widgetSecondaryColor);
          }
          if (data.widgetTextColor) {
            document.documentElement.style.setProperty('--loyalty-text-color', data.widgetTextColor);
          }
        })
        .catch(error => console.error('Error loading program info:', error));
    }
  }, [shop]);

  return (
    <div className={`loyalty-widget ${isExpanded ? 'expanded' : ''}`}>
      <Card>
        <div className="loyalty-widget__header">
          <Text as="h2" variant="headingMd">
            {programInfo?.name || t('loyalty.points.balance')}
          </Text>
          {isVip && (
            <span className="loyalty-widget__vip-badge">
              <Icon source={CheckCircleIcon} />
              VIP
            </span>
          )}
        </div>
        <div className="loyalty-widget__points">
          <Text as="p" variant="bodyLg" fontWeight="bold">{points}</Text>
          <Text as="p" variant="bodyMd">
            {programInfo?.pointsName || t('loyalty.points.label')}
          </Text>
        </div>
        {isExpanded && (
          <div className="loyalty-widget__content">
            <Button
              variant="primary"
              disabled={points === 0}
              onClick={onRedeem}
            >
              {t('loyalty.points.spend')}
            </Button>
          </div>
        )}
      </Card>
      <button
        className="loyalty-widget__toggle"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        {isExpanded ? '▼' : '▲'}
      </button>
    </div>
  );
}
