import { json, type ActionFunctionArgs, type LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData, useSubmit } from "@remix-run/react";
import { authenticate } from "../shopify.server";
import { getExchangeableProducts, addExchangeableProduct, removeExchangeableProduct, updateProductPointsCost, updateProductStatus, calculatePointsCost } from "../models/ExchangeableProducts.server";
import {
  Card,
  Layout,
  Page,
  BlockStack,
  InlineStack,
  Text,
  Button,
  DataTable,
  Badge,
  EmptyState,
  Modal,
  Toast,
  Frame,
} from "@shopify/polaris";
import { AdminLayout } from "../components/Layout/AdminLayout";
import { useState, useCallback } from "react";

import { ProductSelector } from "../components/ProductSelector";
import { useTranslation } from "../hooks/useTranslation";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const { admin, session } = await authenticate.admin(request);
  const { shop } = session;

  try {
    const exchangeableProducts = await getExchangeableProducts(shop);

    // Recalculer les points pour chaque produit selon les paramètres actuels du programme
    const productsWithUpdatedPoints = await Promise.all(
      exchangeableProducts.map(async (product) => {
        const calculatedPointsCost = await calculatePointsCost(shop, product.price || '0');
        return {
          ...product,
          pointsCost: calculatedPointsCost
        };
      })
    );

    return json({ exchangeableProducts: productsWithUpdatedPoints });
  } catch (error) {
    console.error("Error loading exchangeable products:", error);
    return json({ exchangeableProducts: [] });
  }
};

export const action = async ({ request }: ActionFunctionArgs) => {
  const { session } = await authenticate.admin(request);
  const { shop } = session;

  try {
    const formData = await request.formData();
    const action = formData.get('action');

    if (action === 'create') {
      const productsJson = formData.get('products') as string;

      if (!productsJson) {
        return json({ error: "Aucun produit sélectionné" }, { status: 400 });
      }

      try {
        const selectedProducts = JSON.parse(productsJson);
        let successCount = 0;
        let errorCount = 0;

        // Traiter chaque produit sélectionné
        for (const product of selectedProducts) {
          // Calculer le coût en points basé sur le prix et les paramètres du programme
          const calculatedPointsCost = await calculatePointsCost(shop, product.price || '0');

          const productData = {
            shopifyProductId: product.id,
            title: product.title,
            handle: product.handle || product.id,
            image: product.image || '',
            price: product.price || '0',
            pointsCost: calculatedPointsCost,
            active: true
          };

          const success = await addExchangeableProduct(shop, productData);
          if (success) {
            successCount++;
          } else {
            errorCount++;
          }
        }

        if (successCount > 0) {
          const message = errorCount > 0
            ? `${successCount} produit(s) ajouté(s) avec succès, ${errorCount} déjà existant(s)`
            : `${successCount} produit(s) ajouté(s) avec succès`;
          return json({ success: true, message });
        } else {
          return json({ error: "Aucun produit n'a pu être ajouté (déjà existants)" }, { status: 400 });
        }
      } catch (parseError) {
        return json({ error: "Erreur lors du traitement des produits" }, { status: 400 });
      }
    }

    if (action === 'delete') {
      const id = formData.get('id') as string;
      const success = await removeExchangeableProduct(shop, id);
      if (success) {
        return json({ success: true, message: "Produit échangeable supprimé avec succès" });
      } else {
        return json({ error: "Erreur lors de la suppression" }, { status: 500 });
      }
    }

    if (action === 'updatePoints') {
      const id = formData.get('id') as string;
      const pointsCost = parseInt(formData.get('pointsCost') as string);
      const success = await updateProductPointsCost(shop, id, pointsCost);
      if (success) {
        return json({ success: true, message: "Coût en points mis à jour avec succès" });
      } else {
        return json({ error: "Erreur lors de la mise à jour" }, { status: 500 });
      }
    }

    if (action === 'toggleStatus') {
      const id = formData.get('id') as string;
      const currentStatus = formData.get('currentStatus') === 'true';
      const success = await updateProductStatus(shop, id, !currentStatus);
      if (success) {
        return json({ success: true, message: `Produit ${!currentStatus ? 'activé' : 'désactivé'} avec succès` });
      } else {
        return json({ error: "Erreur lors de la mise à jour du statut" }, { status: 500 });
      }
    }

    return json({ error: "Action non reconnue" }, { status: 400 });
  } catch (error) {
    console.error("Error managing exchangeable products:", error);
    return json({ error: "Erreur lors de l'opération" }, { status: 500 });
  }
};

export default function ExchangeableProducts() {
  const { exchangeableProducts } = useLoaderData<typeof loader>();
  const submit = useSubmit();
  const { t } = useTranslation();

  // États pour le modal d'ajout
  const [modalActive, setModalActive] = useState(false);
  const [selectedProducts, setSelectedProducts] = useState<any[]>([]);

  // État pour les toasts
  const [toastActive, setToastActive] = useState(false);
  const [toastMessage, setToastMessage] = useState('');
  const [toastError, setToastError] = useState(false);

  const handleModalToggle = useCallback(() => {
    setModalActive(!modalActive);
    if (!modalActive) {
      // Reset form when opening modal
      setSelectedProducts([]);
    }
  }, [modalActive]);

  const handleSubmit = useCallback(() => {
    if (selectedProducts.length === 0) {
      setToastMessage(t('admin.exchangeableProducts.errorSelectOne'));
      setToastError(true);
      setToastActive(true);
      return;
    }

    // Envoyer tous les produits sélectionnés en une seule requête
    const formData = new FormData();
    formData.append('action', 'create');
    formData.append('products', JSON.stringify(selectedProducts));

    submit(formData, { method: 'post' });
    handleModalToggle();

    setToastMessage(t('admin.exchangeableProducts.successAdd', { count: selectedProducts.length }));
    setToastError(false);
    setToastActive(true);
  }, [selectedProducts, submit, handleModalToggle, t]);

  const handleDelete = useCallback((id: string) => {
    const formData = new FormData();
    formData.append('action', 'delete');
    formData.append('id', id);
    submit(formData, { method: 'post' });

    setToastMessage(t('admin.exchangeableProducts.successDelete'));
    setToastError(false);
    setToastActive(true);
  }, [submit, t]);

  const handleToggleStatus = useCallback((id: string, currentStatus: boolean) => {
    const formData = new FormData();
    formData.append('action', 'toggleStatus');
    formData.append('id', id);
    formData.append('currentStatus', currentStatus.toString());
    submit(formData, { method: 'post' });

    setToastMessage(t('admin.exchangeableProducts.successToggle', { status: !currentStatus ? t('admin.exchangeableProducts.active') : t('admin.exchangeableProducts.inactive') }));
    setToastError(false);
    setToastActive(true);
  }, [submit, t]);

  const toggleToastActive = useCallback(() => setToastActive((active) => !active), []);

  const toastMarkup = toastActive ? (
    <Toast
      content={toastMessage}
      onDismiss={toggleToastActive}
      error={toastError}
    />
  ) : null;

  // Préparer les données pour le tableau
  const rows = exchangeableProducts.map((product: any) => [
    product.image ? (
      <img src={product.image} alt={product.title} style={{ width: '40px', height: '40px', objectFit: 'cover', borderRadius: '4px' }} />
    ) : (
      <div style={{ width: '40px', height: '40px', background: '#f0f0f0', borderRadius: '4px' }} />
    ),
    <div>
      <Text as="p" variant="bodyMd" fontWeight="semibold">{product.title}</Text>
      <Text as="p" variant="bodySm" tone="subdued">{t('admin.productSelector.price')}: {product.price}€</Text>
    </div>,
    <div>
      <Text as="p" variant="bodyMd" fontWeight="semibold" tone="success">
        {product.pointsCost} {t('admin.exchangeableProducts.pointsCost')}
      </Text>
      <Text as="p" variant="bodySm" tone="subdued">
        {t('admin.exchangeableProducts.calculatedAuto') || 'Calculé automatiquement'}
      </Text>
    </div>,
    <Badge tone={product.active ? 'success' : 'critical'}>
      {product.active ? t('admin.exchangeableProducts.active') : t('admin.exchangeableProducts.inactive')}
    </Badge>,
    <InlineStack gap="200">
      <Button
        size="micro"
        variant={product.active ? 'secondary' : 'primary'}
        onClick={() => handleToggleStatus(product.id, product.active)}
      >
        {product.active ? t('admin.exchangeableProducts.deactivate') : t('admin.exchangeableProducts.activate')}
      </Button>
      <Button
        size="micro"
        tone="critical"
        onClick={() => handleDelete(product.id)}
      >
        {t('admin.exchangeableProducts.delete')}
      </Button>
    </InlineStack>
  ]);

  const emptyStateMarkup = exchangeableProducts.length === 0 ? (
    <EmptyState
      heading={t('admin.exchangeableProducts.emptyStateHeading')}
      action={{
        content: t('admin.exchangeableProducts.addProduct'),
        onAction: handleModalToggle,
      }}
      image="https://cdn.shopify.com/s/files/1/0262/4071/2726/files/emptystate-files.png"
    >
      <p>{t('admin.exchangeableProducts.emptyStateDescription')}</p>
    </EmptyState>
  ) : null;

  return (
    <Frame>
      <AdminLayout title={t('admin.exchangeableProducts.title')}>
        <Page fullWidth>
          <Layout>
            <Layout.Section>
              <BlockStack gap="500">
                <Card>
                  <BlockStack gap="400">
                    <InlineStack align="space-between">
                      <div>
                        <Text as="h2" variant="headingMd">
                          {t('admin.exchangeableProducts.title')}
                        </Text>
                        <Text as="p" variant="bodySm" tone="subdued">
                          {t('admin.exchangeableProducts.subtitle')}
                        </Text>
                      </div>
                      <Button variant="primary" onClick={handleModalToggle}>
                        {t('admin.exchangeableProducts.addProduct')}
                      </Button>
                    </InlineStack>
                  </BlockStack>
                </Card>

                <Card>
                  {emptyStateMarkup || (
                    <DataTable
                      columnContentTypes={['text', 'text', 'text', 'text', 'text']}
                      headings={[
                        t('admin.exchangeableProducts.image'),
                        t('admin.exchangeableProducts.product'),
                        t('admin.exchangeableProducts.pointsCost'),
                        t('admin.exchangeableProducts.status'),
                        t('admin.exchangeableProducts.actions'),
                      ] as unknown as React.ReactNode[]}
                      rows={rows}
                    />
                  )}
                </Card>
              </BlockStack>
            </Layout.Section>
          </Layout>
        </Page>
      </AdminLayout>

      <Modal
        open={modalActive}
        onClose={handleModalToggle}
        title={t('admin.exchangeableProducts.modalTitle')}
        primaryAction={{
          content: t('admin.exchangeableProducts.modalPrimary'),
          onAction: handleSubmit,
        }}
        secondaryActions={[{
          content: t('admin.exchangeableProducts.modalSecondary'),
          onAction: handleModalToggle,
        }]}
      >
        <Modal.Section>
          <BlockStack gap="400">
            <Text as="p" variant="bodyMd">
              {t('admin.exchangeableProducts.modalDescription')}
            </Text>

            <ProductSelector
              selectedProducts={selectedProducts}
              onProductsChange={setSelectedProducts}
            />

            {selectedProducts.length > 0 && (
              <div style={{
                padding: '12px',
                background: '#f0f8ff',
                borderRadius: '8px',
                border: '1px solid #e1e8ff'
              }}>
                <Text as="p" variant="bodySm" tone="subdued">
                  {t('admin.exchangeableProducts.selectedCount', { count: selectedProducts.length })}
                </Text>
              </div>
            )}
          </BlockStack>
        </Modal.Section>
      </Modal>

      {toastMarkup}
    </Frame>
  );
}
