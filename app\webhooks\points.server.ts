import { DeliveryMethod } from "@shopify/shopify-api";
import db from "../db.server";
import { awardPointsForOrder } from "../services/pointsService.server";
import { upsertCustomerFromShopify } from "../models/Customer.server";
import { validateReferralPurchase } from "../models/Referral.server";
import { upsertOrder } from "../models/Order.server";
import prisma from "../db.server";

export const POINTS_WEBHOOKS = [
  {
    topic: "ORDERS_CREATE",
    path: "/webhooks/points/order-created",
    deliveryMethod: DeliveryMethod.Http,
  },
  {
    topic: "ORDERS_CANCELLED",
    path: "/webhooks/points/order-cancelled",
    deliveryMethod: DeliveryMethod.Http,
  },
  {
    topic: "ORDERS_FULFILLED",
    path: "/webhooks/points/order-fulfilled",
    deliveryMethod: DeliveryMethod.Http,
  },
];

interface OrderWebhookData {
  id: string;
  customer?: {
    id: string;
    first_name?: string;
    last_name?: string;
    email?: string;
    total_spent?: string;
    orders_count?: number;
    last_order_date?: string;
  };
  total_price: string;
  cancelled_at?: string;
  fulfillment_status?: string;
  financial_status?: string;
  order_status_url?: string;
  discount_codes?: {
    code: string;
    amount: string;
    type: string;
  }[];
  note_attributes?: {
    name: string;
    value: string;
  }[];
  landing_site?: string;
  cart_token?: string;
}

export async function handleOrderCreated(
  _topic: string,
  shop: string,
  webhookRequestBody: string
) {
  const payload = JSON.parse(webhookRequestBody) as OrderWebhookData;

  if (!payload.customer?.id) return;

  // Vérifier s'il y a un parrainage à valider pour ce client
  const amount = parseFloat(payload.total_price);
  const validationResult = await validateReferralPurchase(shop, String(payload.customer.id), amount);

  if (validationResult.success) {
    console.log(`Parrainage validé avec succès pour le client ${payload.customer.id} avec un achat de ${amount}€`);
  } else if (validationResult.error !== "Aucun parrainage en attente trouvé") {
    console.log(`Validation du parrainage échouée: ${validationResult.error}`);
  }

  // Traiter les codes de réduction s'il y en a
  if (payload.discount_codes && Array.isArray(payload.discount_codes) && payload.discount_codes.length > 0) {
    await handleLoyaltyDiscountCodes(payload.discount_codes, payload.customer.id, payload.id, shop);

    // Si des codes de réduction sont utilisés (fidélité ou externes), ne pas attribuer de points normaux
    console.log(`Commande ${payload.id} avec codes de réduction - pas d'attribution de points normaux`);
    return;
  }

  try {
    const amount = parseFloat(payload.total_price);
    const customerId = String(payload.customer.id);

    // Synchroniser le client avec notre base de données
    let customer = null;
    if (payload.customer) {
      customer = await upsertCustomerFromShopify(payload.customer, shop);
    }

    // Si on n'a pas pu synchroniser le client, on ne peut pas créer la commande
    if (!customer) {
      console.error(`Impossible de synchroniser le client ${customerId} pour la commande ${payload.id}`);
      return;
    }

    // Créer l'entrée de commande dans la base de données
    const orderData = {
      orderId: payload.id,
      customerDbId: customer.id,
      shopifyCustomerId: customerId,
      shop: shop,
      total: amount,
      status: payload.fulfillment_status || 'pending',
      paymentStatus: payload.financial_status || 'pending'
    };

    const orderResult = await upsertOrder(orderData);
    if (orderResult) {
      console.log(`Commande ${payload.id} créée/mise à jour en base de données`);
    } else {
      console.error(`Erreur lors de la création de la commande ${payload.id} en base de données`);
    }

    // Utiliser le service pour attribuer les points
    const result = await awardPointsForOrder(shop, customerId, payload.id, amount);

    if (!result) {
      console.log("Aucun point attribué pour cette commande");
    } else {
      console.log(`${result.points} points attribués au client ${customerId} pour la commande ${payload.id}`);
    }
  } catch (error) {
    console.error("Erreur lors du traitement de la commande :", error);
  }
}

export async function handleOrderCancelled(
  _topic: string,
  shop: string,
  webhookRequestBody: string
) {
  const payload = JSON.parse(webhookRequestBody) as OrderWebhookData;

  if (!payload.customer?.id) return;

  try {
    // Mettre à jour le statut de la commande dans la base de données
    const existingOrder = await prisma.order.findFirst({
      where: {
        orderId: payload.id,
        shop: shop,
      },
    });

    if (existingOrder) {
      await prisma.order.update({
        where: {
          id: existingOrder.id,
        },
        data: {
          status: 'cancelled',
          updatedAt: new Date(),
        },
      });
      console.log(`Commande ${payload.id} marquée comme annulée`);
    }

    const pointsEntry = await db.pointsHistory.findFirst({
      where: {
        metadata: {
          contains: payload.id,
        },
        action: "earn",
      },
      include: {
        customer: true,
      },
    });

    if (!pointsEntry) return;

    await db.customer.update({
      where: {
        id: pointsEntry.ledgerId,
      },
      data: {
        points: {
          decrement: pointsEntry.points,
        },
      },
    });

    await db.pointsHistory.create({
      data: {
        ledgerId: pointsEntry.ledgerId,
        action: "cancel",
        points: -pointsEntry.points,
        description: `Points annulés pour la commande #${payload.id}`,
        metadata: JSON.stringify({
          orderId: payload.id,
          cancelledAt: payload.cancelled_at,
        }),
      },
    });
  } catch (error) {
    console.error("Erreur lors de l'annulation des points :", error);
  }
}

export async function handleOrderFulfilled(
  _topic: string,
  shop: string,
  webhookRequestBody: string
) {
  const payload = JSON.parse(webhookRequestBody) as OrderWebhookData;

  if (!payload.customer?.id) return;

  try {
    // Mettre à jour le statut de la commande dans la base de données
    const existingOrder = await prisma.order.findFirst({
      where: {
        orderId: payload.id,
        shop: shop,
      },
    });

    if (existingOrder) {
      await prisma.order.update({
        where: {
          id: existingOrder.id,
        },
        data: {
          status: payload.fulfillment_status || 'fulfilled',
          updatedAt: new Date(),
        },
      });
      console.log(`Commande ${payload.id} marquée comme expédiée`);
    }

    const pointsEntry = await db.pointsHistory.findFirst({
      where: {
        metadata: {
          contains: payload.id.toString(),
        },
        action: "validate",
      },
    });

    if (pointsEntry) return;

    const originalEntry = await db.pointsHistory.findFirst({
      where: {
        metadata: {
          contains: payload.id.toString(),
        },
        action: "earn",
      },
    });

    if (originalEntry) {
      await db.pointsHistory.create({
        data: {
          ledgerId: originalEntry.ledgerId,
          action: "validate",
          points: 0,
          description: `Points validés pour la commande #${payload.id}`,
          metadata: JSON.stringify({
            orderId: payload.id,
            fulfilledAt: new Date().toISOString(),
          }),
        },
      });
    }
  } catch (error) {
    console.error("Erreur lors de la validation des points :", error);
  }
}

/**
 * Traiter les codes de réduction de fidélité utilisés dans une commande
 * @returns boolean - true si au moins un code de fidélité a été trouvé
 */
async function handleLoyaltyDiscountCodes(
  discountCodes: { code: string; amount: string; type: string }[],
  shopifyCustomerId: string,
  orderId: string,
  shop: string
): Promise<boolean> {
  try {
    console.log(`Processing ${discountCodes.length} discount codes for order ${orderId}`);
    let hasLoyaltyCode = false;

    for (const discountCode of discountCodes) {
      const code = discountCode.code;

      if (!code) continue;

      // Chercher si ce code correspond à une récompense de fidélité
      const reward = await db.reward.findFirst({
        where: {
          code: code,
          shop: shop,
          status: 'active'
        },
        include: {
          customer: true
        }
      });

      if (!reward) {
        console.log(`Code ${code} is not a loyalty reward`);
        continue;
      }

      // Vérifier que le code appartient bien au client qui l'utilise
      if (reward.shopifyCustomerId.toString() !== shopifyCustomerId.toString()) {
        console.log(`Code ${code} belongs to customer ${reward.shopifyCustomerId} but used by ${shopifyCustomerId}`);
        continue;
      }

      console.log(`Code ${code} is a valid loyalty reward for customer ${shopifyCustomerId}`);
      hasLoyaltyCode = true; // Marquer qu'on a trouvé un code de fidélité

      // Marquer la récompense comme utilisée
      // @ts-ignore
      await db.reward.update({
        where: {
          id: reward.id
        },
        data: {
          status: 'used',
          usedAt: new Date(),
          orderId: orderId.toString() // Convertir en string
        }
      });

      console.log(`✅ Marked reward ${reward.id} (code: ${code}) as used for order ${orderId}`);

      // Enregistrer dans l'historique des points
      await prisma.pointsHistory.create({
        data: {
          ledgerId: reward.customerId,
          action: 'code_used',
          points: 0, // Pas de changement de points, juste un événement
          description: `Code de réduction utilisé: ${code}`,
          metadata: JSON.stringify({
            orderId: orderId,
            discountCode: code,
            rewardId: reward.id,
            discountAmount: discountCode.amount || '0',
            discountType: discountCode.type || 'unknown'
          })
        }
      });

      console.log(`📝 Added history entry for code usage: ${code}`);
    }

    return hasLoyaltyCode;

  } catch (error) {
    console.error("Error handling loyalty discount codes:", error);
    return false;
  }
}


