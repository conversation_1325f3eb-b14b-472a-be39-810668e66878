{"loyalty": {"points": {"balance": "Available Points", "earn": "Earn {{points}} points", "spend": "Spend your points"}, "referral": {"invite": "Invite a friend", "reward": "Earn {{points}} points for each referral"}, "vip": {"status": "VIP Status", "progress": "Progress to VIP status"}}, "common": {"save": "Save", "cancel": "Cancel", "loading": "Loading...", "error": "An error occurred", "search": "Search...", "logout": "Logout", "noResults": "No results found", "edit": "Edit", "delete": "Delete", "add": "Add", "create": "Create", "update": "Update", "view": "View", "back": "Back", "next": "Next", "previous": "Previous", "close": "Close", "confirm": "Confirm", "yes": "Yes", "no": "No", "active": "Active", "inactive": "Inactive", "enabled": "Enabled", "disabled": "Disabled", "success": "Success", "warning": "Warning", "info": "Information", "name": "Name", "description": "Description", "status": "Status", "actions": "Actions", "settings": "Settings", "configuration": "Configuration", "all": "All"}, "admin": {"navigation": {"dashboard": "Dashboard", "program": "Program", "customers": "Customers", "analytics": "Analytics", "settings": "Settings", "promotions": "Promotions", "history": "History", "pointsShop": "Points Shop", "overview": "Overview", "pointsConfig": "Points Configuration", "referrals": "Referrals", "vipProgram": "VIP Program", "bonusCampaigns": "Bonus Campaigns", "generalSettings": "General Settings", "customizeWidget": "Customize Widget", "exchangeableProducts": "Exchangeable Products"}, "dashboard": {"title": "Dashboard", "totalPoints": "Total Points", "activeMembers": "Active Members", "redemptionRate": "Redemption Rate", "pointsEvolution": "Points Evolution (30 days)", "pointsDistribution": "Points Distribution by Type", "recentActivity": "Recent Activity", "loadingChart": "Loading chart...", "tableHeaders": {"customer": "Customer", "action": "Action", "points": "Points", "date": "Date"}, "averagePointsPerCustomer": "Average points / customer", "dataAvailableSoon": "Data will be available soon...", "pointsInCirculation": "Points in circulation", "rewardsThisMonth": "Rewards this month"}, "program": {"title": "Loyalty Program", "overview": "Overview", "status": {"active": "Program Active", "inactive": "Program Inactive", "activate": "Activate", "deactivate": "Deactivate", "activeDescription": "Your loyalty program is currently active and your customers can earn points.", "inactiveDescription": "Your program is currently inactive. Activate it to allow your customers to earn points."}, "generalConfiguration": "General Configuration", "programName": "Program Name", "programDescription": "Program Description", "quickActions": "Quick Actions", "pointsConfiguration": "Points Configuration", "referralProgram": "Referral Program", "stats": {"title": "Statistics", "totalCustomers": "Total Customers", "activeCustomers": "Active Customers", "totalPointsEarned": "Total Points Earned", "totalPointsRedeemed": "Total Points Redeemed", "totalRewards": "Total Rewards", "pendingReferrals": "Pending Referrals", "completedReferrals": "Completed Referrals", "pointsDistributed": "Points Distributed"}, "paramSaveSuccess": "Parameters saved successfully", "paramSaveError": "Error saving parameters", "saveModifications": "Save Modifications", "saveDescription": "You have unsaved modifications"}, "customers": {"title": "Customers", "member": "Member", "guest": "Guest", "points": "points", "referrals": "person(s)", "email": "Email", "joinedOn": "Joined on", "type": "Type", "filters": {"type": "Type", "search": "Search by name or email"}, "pagination": "Page {{current}} of {{total}}", "totalCustomers": "{{total}} customer(s) total", "filteredFrom": "filtered from {{total}}", "back": "Back", "viewInShopify": "View in Shopify", "infoTitle": "Customer information", "activityTitle": "Activity", "pointsTab": "Points", "referralsTab": "Referrals", "rewardsTab": "Rewards", "ordersTitle": "Orders", "orderId": "Order ID", "total": "Total", "status": "Status", "date": "Date", "noOrders": "No orders", "currentBalance": "Current balance", "statsTitle": "Statistics", "totalSpent": "Total spent", "ordersCount": "Orders", "completedReferrals": "Referrals", "referralTitle": "Referral", "referralFeatureComing": "Feature coming soon", "referralCodeInfo": "Referral code and link will be shown here", "action": "Action", "referee": "Referee", "referralStatus": "Status", "referralOrderTotal": "Order total", "reward": "<PERSON><PERSON>", "code": "Code", "noRewards": "No rewards redeemed", "none": "—", "earned": "Earned", "redeemed": "Redeemed", "signup": "Signup", "validated": "Validated", "pending": "Pending", "paid": "Paid", "anonymous": "Anonymous customer", "client": "Customer"}, "analytics": {"title": "Analytics", "subtitle": "Loyalty Program Analytics", "memberStats": "Member Statistics", "totalMembers": "Total Members", "newMembersLast30Days": "New Members (30 days)", "pointsTransactions": "Points Transactions", "totalTransactions": "Total Transactions", "pointsDistributed": "Points Distributed", "referralPurchases": "Referral Purchases", "referralRevenue": "Referral Revenue", "trends": "Trends", "membersGrowth": "Members Growth", "pointsGrowth": "Points Growth", "revenueGrowth": "Revenue Growth"}, "settings": {"title": "Settings", "quickNavigation": "Quick Navigation", "customizeWidget": "Customize Widget", "exchangeableProducts": "Exchangeable Products", "generalSettings": "General Settings", "shopName": "Shop Name", "currency": "<PERSON><PERSON><PERSON><PERSON>", "language": "Language", "emailNotifications": "Email Notifications", "pointsName": "Points Name", "welcomeMessage": "Welcome Message", "saveSuccess": "Setting<PERSON> saved successfully", "saveError": "Error saving settings", "customizationTitle": "Customization", "pointsNameHelp": "This name will be used throughout the application", "welcomeMessageHelp": "Message displayed to new customers", "notificationsTitle": "Notifications", "senderEmail": "Sender email", "saveCardTitle": "💾 Save changes", "saveButton": "Save changes", "noChanges": "No changes", "unsavedChanges": "You have unsaved changes"}, "points": {"title": "Points Configuration", "waysToEarn": "Ways to Earn Points", "waysToRedeem": "Ways to Redeem Points", "addWayToEarn": "Add Way to Earn", "addWayToRedeem": "Add Way to Redeem", "earnDescription": "Configure the different ways your customers can earn points. You can create actions with points per euro spent (e.g., 5 points/€1) or fixed points for specific actions (e.g., 100 points for registration).", "redeemDescription": "Configure the rewards your customers can get in exchange for their points.", "minPoints": "From {{points}} points", "exactPoints": "{{points}} points", "configurable": "Configurable", "fixed": "Fixed", "baseSettings": "Base settings", "earningRateLabel": "Earning rate (points/€)", "redemptionRateLabel": "Redemption rate (points/€)", "redemptionRateHelp": "Number of points required for €1 discount (e.g. 100 points = €1)", "minimumPointsLabel": "Minimum points to redeem", "expirationDaysLabel": "Points expiration (days)", "referralPointsLabel": "Referral points", "birthdayPointsLabel": "Birthday points", "save": "Save", "previewTitle": "Preview", "previewAmountLabel": "Purchase amount (€)", "previewForAmount": "For a purchase of {amount}€:", "previewPoints": "Points earned: {points} points", "previewValue": "Value in €: {value}€", "waysToEarnTitle": "Ways to earn points", "waysToEarnDescription": "Configure the different ways your customers can earn points. You can create actions with points per euro spent (e.g. 5 points/€1) or fixed points for specific actions (e.g. 100 points for signup).", "fixedPoints": "{points} fixed points", "pointsPerEuro": "{points} points per €1 spent", "active": "Active", "inactive": "Inactive", "edit": "Edit", "seeAllWaysToEarn": "See all ways to earn", "seeAllWaysToEarnCount": "See all ways to earn ({count})", "waysToRedeemTitle": "Ways to redeem points", "waysToRedeemDescription": "Configure the different rewards your customers can get by redeeming their points. For example, discounts on their orders, free products, or free shipping.", "fromPoints": "From {points} points", "pointsCost": "{points} points", "seeAllWaysToRedeem": "See all ways to redeem", "seeAllWaysToRedeemCount": "See all ways to redeem ({count})", "successUpdate": "Settings updated successfully", "errorUpdate": "Error updating settings", "errorAllFieldsRequired": "All fields are required", "errorAllNumbers": "All values must be valid numbers", "errorPositiveValues": "Values must be positive"}, "referrals": {"title": "Referral Program", "description": "The referral program allows your loyal customers to recommend your store to their friends and family. When a customer refers a friend who makes their first purchase, both parties receive rewards. It's a great way to acquire new customers while rewarding your existing customers' loyalty.", "howItWorks": "How it works:", "step1": "The referrer shares their unique referral code with friends", "step2": "The referred friend uses this code on their first order", "step3": "If the order meets the minimum amount, rewards are distributed", "step4": "Both referrer and referred receive their respective rewards", "programStatus": "Program status", "active": "Active", "inactive": "Inactive", "activate": "Activate", "deactivate": "Deactivate", "activeDescription": "Your customers can currently refer friends and earn rewards.", "inactiveDescription": "The referral program is currently disabled. Enable it to allow your customers to refer friends.", "referrerRewardTitle": "Referrer reward", "referrerGets": "The referrer gets {reward}", "referredRewardTitle": "Referred reward", "referredGets": "The referred gets {reward}", "rewardTypeLabel": "Reward type", "rewardTypePoints": "Points", "rewardTypeFixed": "Fixed discount (€)", "rewardTypeDiscount": "Percentage (%)", "rewardAmountPoints": "Amount in points", "rewardAmountFixed": "Discount amount (€)", "rewardAmountDiscount": "Discount percentage (%)", "rewardAmountDefault": "Reward amount", "conditionsTitle": "Conditions", "minPurchaseLabel": "Minimum purchase amount for referred (€)", "minPurchaseHelp": "Minimum amount the referred must spend to validate the referral", "expiryDaysLabel": "Invitation validity period (days)", "expiryDaysHelp": "Number of days the invitation remains valid", "customizationTitle": "Customization", "customMessageLabel": "Invitation message", "customMessageHelp": "This message will be displayed on the referral page", "save": "Save changes", "referralLinksTitle": "Referral link management", "referralLinksDescription": "Generate and manage referral links for your customers.", "customerTableName": "Name", "customerTableEmail": "Email", "customerTableType": "Type", "customerTablePoints": "Points", "customerTableStatus": "Referral status", "customerTableAction": "Action", "member": "Member", "guest": "Guest", "linkActive": "Active link", "noLink": "No link", "generateLink": "Generate link", "existingLink": "Existing link", "noCustomers": "No customers found. Make sure you have customers in your store.", "linkFormatTitle": "Referral link format", "linkFormatDescription": "Generated links follow this format:", "linkFormatExample": "https://your-store.myshopify.com?ref=eyxxxxxxxxxxxxxxxx", "linkFormatHelp": "Where \"eyxxxxxxxxxxxxxxxx\" is a unique secure base64 token.", "linkHowItWorks": "How it works:", "linkStep1": "The customer generates their link via the loyalty widget", "linkStep2": "They share the link on social media or by email", "linkStep3": "A friend clicks the link and is redirected to your store", "linkStep4": "The code is automatically captured and stored", "linkStep5": "On purchase, the referral is validated and rewards are distributed", "statsTitle": "Referral statistics", "statsTotal": "Total referrals", "statsCompleted": "Completed", "statsPending": "Pending", "statsConversion": "Conversion rate", "helpTitle": "Help", "helpDescription1": "The referral program allows your customers to recommend your store to their friends. Referrers and referred friends receive rewards when the referral is validated.", "helpDescription2": "A referral is validated when the referred friend makes their first purchase reaching the defined minimum amount."}, "widget": {"title": "Widget Customization", "appearance": "Appearance", "colors": "Colors", "position": {"label": "Widget Position", "bottomRight": "Bottom right", "bottomLeft": "Bottom left", "topRight": "Top right", "topLeft": "Top left"}, "size": {"label": "Widget Size", "small": "Small", "medium": "Medium", "large": "Large"}, "borders": {"label": "Borders", "square": "Square", "rounded": "Rounded", "pill": "<PERSON>ll"}, "shadow": "Drop Shadow", "animation": "Animation", "showPointsOnButton": "Show Points on Button", "primaryColor": "Primary Color", "secondaryColor": "Secondary Color", "textColor": "Text Color", "preview": "Preview", "previewDescription": "Real-time preview of your widget", "loyaltyProgram": "Loyalty Program", "welcomeTo": "Welcome to", "welcomeMessage": "Welcome to our loyalty program!", "guest": "Guest", "member": "Member", "points": "Points", "orders": "Orders", "nextReward": "Next reward", "pointsNeeded": "{{count}} points needed", "yourRewards": "Your rewards", "oneRewardAvailable": "You have 1 reward available", "waysToEarn": "Ways to earn", "waysToRedeem": "Ways to redeem", "referFriends": "Refer your friends", "referralsCompleted": "{{count}} referrals completed", "shareUrl": "Share this URL to give your friends the reward €4 off coupon", "facebook": "Facebook", "x": "X", "email": "Email", "yourActivity": "Your activity", "poweredBy": "Powered by <PERSON><PERSON><PERSON>"}, "notifications": {"languageChanged": "Language changed to {{language}}"}, "exchangeableProducts": {"title": "Exchangeable Products", "subtitle": "Manage the products your customers can get in exchange for points", "addProduct": "Add product", "emptyStateHeading": "No exchangeable product configured", "emptyStateDescription": "Start by adding products your customers can redeem for points.", "product": "Product", "image": "Image", "pointsCost": "Points cost", "status": "Status", "actions": "Actions", "calculatedAuto": "Calculated automatically", "active": "Active", "inactive": "Inactive", "activate": "Activate", "deactivate": "Deactivate", "delete": "Delete", "successAdd": "Adding {count} product(s)...", "successDelete": "Exchangeable product deleted successfully", "successToggle": "Product status {status} successfully", "errorSelectOne": "Please select at least one product", "modalTitle": "Add exchangeable product", "modalPrimary": "Add", "modalSecondary": "Cancel", "modalDescription": "Select the products your customers can redeem for points. The points cost will be set in the program settings.", "selectedCount": "{count} product(s) selected. The points cost will be set automatically according to the program settings."}, "productSelector": {"title": "Products exchangeable with points", "addProducts": "Add products", "description": "Select the products your customers can buy with their loyalty points.", "empty": "No product selected. Click 'Add products' to start.", "price": "Price", "remove": "Remove", "selectProducts": "Select products", "close": "Close", "searchLabel": "Search products", "searchPlaceholder": "Product name...", "loading": "Loading products...", "selected": "Selected"}}}