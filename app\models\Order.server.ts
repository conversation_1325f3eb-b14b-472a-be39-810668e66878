import prisma from "../db.server";

export interface OrderData {
  id: string;
  orderId: string;
  customerDbId: string;
  shopifyCustomerId: string;
  shop: string;
  total: number;
  status: string;
  paymentStatus: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateOrderData {
  orderId: string;
  customerDbId: string;
  shopifyCustomerId: string;
  shop: string;
  total: number;
  status: string;
  paymentStatus: string;
}

/**
 * Créer une nouvelle commande dans la base de données
 */
export async function createOrder(data: CreateOrderData): Promise<OrderData | null> {
  try {
    return await prisma.order.create({
      data: {
        orderId: data.orderId,
        customerDbId: data.customerDbId,
        shopifyCustomerId: data.shopifyCustomerId,
        shop: data.shop,
        total: data.total,
        status: data.status,
        paymentStatus: data.paymentStatus,
      },
    });
  } catch (error) {
    console.error("Error creating order:", error);
    return null;
  }
}

/**
 * Récupérer une commande par son ID Shopify
 */
export async function getOrderByShopifyId(orderId: string, shop: string): Promise<OrderData | null> {
  try {
    return await prisma.order.findFirst({
      where: {
        orderId,
        shop,
      },
    });
  } catch (error) {
    console.error("Error fetching order by Shopify ID:", error);
    return null;
  }
}

/**
 * Mettre à jour une commande existante
 */
export async function updateOrder(
  orderId: string,
  shop: string,
  data: Partial<CreateOrderData>
): Promise<OrderData | null> {
  try {
    return await prisma.order.update({
      where: {
        orderId_shop: {
          orderId,
          shop,
        },
      },
      data,
    });
  } catch (error) {
    console.error("Error updating order:", error);
    return null;
  }
}

/**
 * Créer ou mettre à jour une commande (upsert)
 */
export async function upsertOrder(data: CreateOrderData): Promise<OrderData | null> {
  try {
    return await prisma.order.upsert({
      where: {
        orderId_shop: {
          orderId: data.orderId,
          shop: data.shop,
        },
      },
      update: {
        total: data.total,
        status: data.status,
        paymentStatus: data.paymentStatus,
        updatedAt: new Date(),
      },
      create: data,
    });
  } catch (error) {
    console.error("Error upserting order:", error);
    return null;
  }
}

/**
 * Récupérer toutes les commandes d'un client
 */
export async function getCustomerOrders(customerDbId: string): Promise<OrderData[]> {
  try {
    return await prisma.order.findMany({
      where: {
        customerDbId,
      },
      orderBy: {
        createdAt: "desc",
      },
    });
  } catch (error) {
    console.error("Error fetching customer orders:", error);
    return [];
  }
}

/**
 * Récupérer toutes les commandes d'un shop
 */
export async function getShopOrders(shop: string, limit: number = 50): Promise<OrderData[]> {
  try {
    return await prisma.order.findMany({
      where: {
        shop,
      },
      orderBy: {
        createdAt: "desc",
      },
      take: limit,
    });
  } catch (error) {
    console.error("Error fetching shop orders:", error);
    return [];
  }
}
