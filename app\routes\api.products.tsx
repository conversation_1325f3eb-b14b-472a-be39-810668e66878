import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { getShopifyProducts, searchShopifyProducts } from "../models/ShopifyProducts.server";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  try {
    const url = new URL(request.url);
    const searchQuery = url.searchParams.get("search");

    let products;
    if (searchQuery && searchQuery.trim() !== "") {
      // Recherche de produits
      products = await searchShopifyProducts(request, searchQuery.trim());
    } else {
      // Récupérer tous les produits
      products = await getShopifyProducts(request);
    }

    return json({ products });
  } catch (error) {
    console.error("Error in products API:", error);
    return json({ products: [], error: "Erreur lors de la récupération des produits" }, { status: 500 });
  }
};
