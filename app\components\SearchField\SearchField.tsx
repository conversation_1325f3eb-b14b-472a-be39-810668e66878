import { useState, useCallback, useEffect, useRef } from "react";
import { TextField } from "@shopify/polaris";
import { useNavigate } from "@remix-run/react";
import { useTranslation } from "../../hooks/useTranslation";

interface SearchResult {
  id: string;
  customerId: string;
  name: string;
  email: string;
  points: number;
  type: string;
  url: string;
}

export function SearchField() {
  const [searchValue, setSearchValue] = useState("");
  const [searchActive, setSearchActive] = useState(false);
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const { t } = useTranslation();
  const navigate = useNavigate();
  const searchRef = useRef<HTMLDivElement>(null);

  // Debug: Log pour vérifier que le composant fonctionne
  console.log("SearchField rendered, searchValue:", searchValue);

  // Fonction de recherche avec debounce
  useEffect(() => {
    const timeoutId = setTimeout(async () => {
      if (searchValue.trim()) {
        setIsSearching(true);
        try {
          const response = await fetch(`/api/search/customers?q=${encodeURIComponent(searchValue)}`);
          const results = await response.json();
          setSearchResults(results);
          setSearchActive(true);
        } catch (error) {
          console.error("Search error:", error);
          setSearchResults([]);
        } finally {
          setIsSearching(false);
        }
      } else {
        setSearchResults([]);
        setSearchActive(false);
      }
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [searchValue]);

  const handleSearchChange = useCallback((value: string) => {
    console.log("handleSearchChange called with:", value); // Debug log
    setSearchValue(value);
    if (!value.trim()) {
      setSearchActive(false);
      setSearchResults([]);
    }
  }, []);

  const handleSearchResultClick = useCallback((url: string) => {
    navigate(url);
    setSearchActive(false);
    setSearchValue("");
    setSearchResults([]);
  }, [navigate]);

  const handleClearSearch = useCallback(() => {
    setSearchValue("");
    setSearchActive(false);
    setSearchResults([]);
  }, []);

  // Gestion des clics extérieurs pour fermer les résultats
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setSearchActive(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <div ref={searchRef} style={{ width: "300px", height: "50px", position: "relative", display: "inline-flex",flexDirection: "column",justifyContent: "center" }}>
      <TextField
        value={searchValue}
        onChange={handleSearchChange}
        placeholder={t("common.search")}
        autoComplete="off"
        clearButton
        onClearButtonClick={handleClearSearch}
      />

      {/* Résultats de recherche */}
      {searchActive && (
        <div style={{
          position: "absolute",
          top: "100%",
          left: 0,
          right: 0,
          backgroundColor: "white",
          border: "1px solid #c9cccf",
          borderRadius: "4px",
          boxShadow: "0 4px 8px rgba(0,0,0,0.1)",
          zIndex: 1000,
          maxHeight: "300px",
          overflowY: "auto"
        }}>
          {searchResults.length > 0 ? (
            searchResults.map((customer) => (
              <div
                key={customer.id}
                onClick={() => handleSearchResultClick(customer.url)}
                style={{
                  padding: "12px 16px",
                  cursor: "pointer",
                  borderBottom: "1px solid #f1f2f3",
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                  transition: "background-color 0.2s"
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = "#f6f6f7";
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = "white";
                }}
              >
                <div>
                  <div style={{ fontWeight: "500", fontSize: "14px", marginBottom: "2px" }}>
                    {customer.name || customer.email}
                  </div>
                  <div style={{ fontSize: "12px", color: "#6d7175" }}>
                    {customer.email} • {customer.points} points
                  </div>
                </div>
                <div style={{
                  fontSize: "11px",
                  padding: "2px 8px",
                  borderRadius: "12px",
                  backgroundColor: customer.type === "member" ? "#d4edda" : "#e2e3e5",
                  color: customer.type === "member" ? "#155724" : "#495057"
                }}>
                  {customer.type === "member" ? t("admin.customers.member") : t("admin.customers.guest")}
                </div>
              </div>
            ))
          ) : searchValue.trim() && !isSearching ? (
            <div style={{ padding: "16px", textAlign: "center", color: "#6d7175" }}>
              {t("common.noResults")}
            </div>
          ) : isSearching ? (
            <div style={{ padding: "16px", textAlign: "center", color: "#6d7175" }}>
              {t("common.loading")}
            </div>
          ) : null}
        </div>
      )}
    </div>
  );
}
