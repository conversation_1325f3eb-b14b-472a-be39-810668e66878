import { json, type ActionFunctionArgs, type LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData, useSubmit } from "@remix-run/react";
import { useState, useCallback } from "react";
import { authenticate } from "../shopify.server";
import { getReferralSettings, upsertReferralSettings, getDefaultReferralSettings } from "../models/ReferralSettings.server";
import { getProgramStats } from "../models/ProgramStats.server";
import { createReferralLink, getReferralStats, hasActiveReferralLink } from "../models/Referral.server";
import { getAllCustomers } from "../models/Customer.server";
import {
  Layout,
  Card,
  FormLayout,
  TextField,
  Button,
  BlockStack,
  Text,
  Banner,
  Box,
  InlineStack,
  Icon,
  ChoiceList,
  Select,
  Badge,
  List,
  Toast,
  Frame,
  DataTable,
  Divider,
} from "@shopify/polaris";
import { GiftCardIcon } from "@shopify/polaris-icons";
import { AdminLayout } from "app/components/Layout/AdminLayout";
import { useTranslation } from "../hooks/useTranslation";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const { session } = await authenticate.admin(request);
  const shop = session.shop;

  try {
    // Récupérer les paramètres de parrainage existants
    let settings = await getReferralSettings(shop);

    // Si aucun paramètre n'existe, utiliser les valeurs par défaut
    if (!settings) {
      settings = getDefaultReferralSettings();
    }

    // Récupérer les statistiques du programme et les clients
    const [stats, referralStats, customersData] = await Promise.all([
      getProgramStats(shop),
      getReferralStats(shop),
      getAllCustomers(shop, 1, 10) // Récupérer les 10 premiers clients
    ]);

    // Vérifier quels clients ont déjà des liens de parrainage
    const customersWithReferralStatus = await Promise.all(
      customersData.customers.map(async (customer) => ({
        ...customer,
        hasReferralLink: await hasActiveReferralLink(shop, customer.customerId)
      }))
    );

    return json({ settings, stats, referralStats, customers: customersWithReferralStatus });
  } catch (error) {
    console.error("Error loading referral settings:", error);
    // En cas d'erreur, retourner les paramètres par défaut
    return json({
      settings: getDefaultReferralSettings(),
      stats: {},
      referralStats: { total: 0, completed: 0, pending: 0, conversionRate: 0 },
      customers: []
    });
  }
};

export const action = async ({ request }: ActionFunctionArgs) => {
  const { session } = await authenticate.admin(request);
  const shop = session.shop;

  const formData = await request.formData();
  const actionType = formData.get("actionType") as string;

  try {
    if (actionType === "toggle") {
      // Basculer l'état actif/inactif
      const currentSettings = await getReferralSettings(shop) || getDefaultReferralSettings();
      const updatedSettings = {
        ...currentSettings,
        active: !currentSettings.active
      };

      await upsertReferralSettings(shop, updatedSettings);
      return json({ success: true, message: `Programme ${updatedSettings.active ? 'activé' : 'désactivé'}` });
    }

    if (actionType === "generateReferral") {
      // Générer un lien de parrainage pour un client
      const customerId = formData.get("customerId") as string;
      if (!customerId) {
        return json({ error: "Customer ID required" }, { status: 400 });
      }

      const result = await createReferralLink(shop, customerId);
      if (result.success) {
        return json({ success: true, referralUrl: result.referralUrl, message: "Lien de parrainage généré avec succès" });
      } else {
        return json({ error: result.error }, { status: 400 });
      }
    }

    if (actionType === "save") {
      // Sauvegarder tous les paramètres
      const settingsData = {
        active: formData.get("active") === "true",
        referrerReward: {
          type: formData.get("referrerRewardType") as "points" | "discount" | "fixed",
          amount: parseInt(formData.get("referrerRewardAmount") as string) || 0
        },
        referredReward: {
          type: formData.get("referredRewardType") as "points" | "discount" | "fixed",
          amount: parseInt(formData.get("referredRewardAmount") as string) || 0
        },
        minimumPurchase: parseInt(formData.get("minimumPurchase") as string) || 0,
        expiryDays: parseInt(formData.get("expiryDays") as string) || 30,
        customMessage: formData.get("customMessage") as string || ""
      };

      await upsertReferralSettings(shop, settingsData);
      return json({ success: true, message: "Paramètres sauvegardés avec succès" });
    }

    return json({ error: "Action non reconnue" }, { status: 400 });
  } catch (error) {
    console.error("Error saving referral settings:", error);
    return json({ error: "Erreur lors de la sauvegarde" }, { status: 500 });
  }
};

export default function ProgramReferrals() {
  const loaderData = useLoaderData<typeof loader>();
  const initialSettings = loaderData.settings;
  const stats = (loaderData as any).stats;
  const referralStats = (loaderData as any).referralStats;
  const customers = (loaderData as any).customers;
  const submit = useSubmit();

  // État local pour les formulaires
  const [settings, setSettings] = useState(initialSettings);
  const [hasChanges, setHasChanges] = useState(false);
  const [toastActive, setToastActive] = useState(false);
  const [toastMessage, setToastMessage] = useState("");
  const { t } = useTranslation();

  const showToast = (message: string) => {
    setToastMessage(message);
    setToastActive(true);
  };

  const handleGenerateReferral = (customerId: string) => {
    const formData = new FormData();
    formData.append("actionType", "generateReferral");
    formData.append("customerId", customerId);

    submit(formData, {
      method: "post",
      preventScrollReset: true
    });
  };

  const handleSubmit = (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    const formData = new FormData(event.currentTarget);
    formData.append("actionType", "save");
    formData.append("active", settings.active.toString());
    formData.append("referrerRewardType", settings.referrerReward.type);
    formData.append("referrerRewardAmount", settings.referrerReward.amount.toString());
    formData.append("referredRewardType", settings.referredReward.type);
    formData.append("referredRewardAmount", settings.referredReward.amount.toString());
    formData.append("minimumPurchase", settings.minimumPurchase.toString());
    formData.append("expiryDays", settings.expiryDays.toString());
    formData.append("customMessage", settings.customMessage);

    submit(formData, {
      method: "post",
      preventScrollReset: true
    });

    setHasChanges(false);
    showToast("Paramètres sauvegardés avec succès !");
  };

  const handleToggleActive = () => {
    const formData = new FormData();
    formData.append("actionType", "toggle");

    submit(formData, {
      method: "post",
      preventScrollReset: true
    });

    // Mettre à jour l'état local immédiatement
    setSettings(prev => ({ ...prev, active: !prev.active }));
    showToast(settings.active ? "Programme désactivé" : "Programme activé");
  };

  // Handlers pour mettre à jour l'état
  const updateSetting = useCallback((field: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [field]: value
    }));
    setHasChanges(true);
  }, []);

  const updateNestedSetting = useCallback((parent: keyof typeof settings, field: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [parent]: {
        ...(prev[parent] as any),
        [field]: value
      }
    }));
    setHasChanges(true);
  }, [settings]);

  const rewardTypes = [
    { label: t('admin.referrals.rewardTypePoints'), value: "points" },
    { label: t('admin.referrals.rewardTypeFixed'), value: "fixed" },
    { label: t('admin.referrals.rewardTypeDiscount'), value: "discount" },
  ];

  // Fonctions pour les textes dynamiques
  const getRewardLabel = (type: string) => {
    switch (type) {
      case "points": return t('admin.referrals.rewardAmountPoints');
      case "fixed": return t('admin.referrals.rewardAmountFixed');
      case "discount": return t('admin.referrals.rewardAmountDiscount');
      default: return t('admin.referrals.rewardAmountDefault');
    }
  };

  const getRewardText = (amount: number, type: string) => {
    switch (type) {
      case "points": return t('admin.referrals.rewardText.points', { amount });
      case "fixed": return t('admin.referrals.rewardText.fixed', { amount });
      case "discount": return t('admin.referrals.rewardText.discount', { amount });
      default: return t('admin.referrals.rewardText.default', { amount });
    }
  };

  const toastMarkup = toastActive ? (
    <Toast
      content={toastMessage}
      onDismiss={() => setToastActive(false)}
      duration={4000}
    />
  ) : null;

  return (
    <Frame>
      <AdminLayout title={t('admin.referrals.title')}>
      <Layout>
        <Layout.Section>
          <BlockStack gap="500">
            {/* Description du programme de parrainage */}
            <Card>
              <BlockStack gap="400">
                <Text as="h2" variant="headingMd">{t('admin.referrals.title')}</Text>
                <Text as="p" variant="bodyMd" tone="subdued">{t('admin.referrals.description')}</Text>
                <Text as="p" variant="bodyMd" tone="subdued">
                  <strong>{t('admin.referrals.howItWorks')}</strong>
                </Text>
                <List type="number">
                  <List.Item>{t('admin.referrals.step1')}</List.Item>
                  <List.Item>{t('admin.referrals.step2')}</List.Item>
                  <List.Item>{t('admin.referrals.step3')}</List.Item>
                  <List.Item>{t('admin.referrals.step4')}</List.Item>
                </List>
              </BlockStack>
            </Card>

            <Card>
              <BlockStack gap="400">
                <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}>
                  <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
                <Text as="h2" variant="headingMd">{t('admin.referrals.programStatus')}</Text>
                <Badge tone={settings.active ? "success" : "attention"}>
                  {settings.active ? t('admin.referrals.active') : t('admin.referrals.inactive')}
                </Badge>
                  </div>
                <Button
                  tone={settings.active ? "critical" : "success"}
                  onClick={handleToggleActive}
                >
                  {settings.active ? t('admin.referrals.deactivate') : t('admin.referrals.activate')}
                </Button>
                </div>
                <Text as="p" variant="bodyMd" tone="subdued">
                  {settings.active
                    ? t('admin.referrals.activeDescription')
                    : t('admin.referrals.inactiveDescription')
                  }
                </Text>
              </BlockStack>
            </Card>

            <Card>
              <BlockStack gap="400">
                <Text as="h2" variant="headingMd">{t('admin.referrals.referrerRewardTitle')}</Text>
                <Box paddingBlockEnd="400">
                  <InlineStack gap="200" align="center">
                    <Icon source={GiftCardIcon} />
                    <Text as="span" variant="bodyMd">
                      {t('admin.referrals.referrerGets', { reward: getRewardText(settings.referrerReward.amount, settings.referrerReward.type) })}
                    </Text>
                  </InlineStack>
                </Box>
                <form onSubmit={handleSubmit}>
                  <FormLayout>
                    <Select
                      label={t('admin.referrals.rewardTypeLabel')}
                      options={rewardTypes}
                      name="referrerRewardType"
                      value={settings.referrerReward.type}
                      onChange={(value) => updateNestedSetting('referrerReward', 'type', value)}
                    />
                    <TextField
                      label={getRewardLabel(settings.referrerReward.type)}
                      type="number"
                      name="referrerRewardAmount"
                      value={settings.referrerReward.amount.toString()}
                      onChange={(value) => updateNestedSetting('referrerReward', 'amount', parseInt(value) || 0)}
                      autoComplete="off"
                      min={0}
                      max={settings.referrerReward.type === "discount" ? 100 : undefined}
                    />
                  </FormLayout>
                </form>
              </BlockStack>
            </Card>

            <Card>
              <BlockStack gap="400">
                <Text as="h2" variant="headingMd">{t('admin.referrals.referredRewardTitle')}</Text>
                <Box paddingBlockEnd="400">
                  <InlineStack gap="200" align="center">
                    <Icon source={GiftCardIcon} />
                    <Text as="span" variant="bodyMd">
                      {t('admin.referrals.referredGets', { reward: getRewardText(settings.referredReward.amount, settings.referredReward.type) })}
                    </Text>
                  </InlineStack>
                </Box>
                <form onSubmit={handleSubmit}>
                  <FormLayout>
                    <Select
                      label={t('admin.referrals.rewardTypeLabel')}
                      options={rewardTypes}
                      name="referredRewardType"
                      value={settings.referredReward.type}
                      onChange={(value) => updateNestedSetting('referredReward', 'type', value)}
                    />
                    <TextField
                      label={getRewardLabel(settings.referredReward.type)}
                      type="number"
                      name="referredRewardAmount"
                      value={settings.referredReward.amount.toString()}
                      onChange={(value) => updateNestedSetting('referredReward', 'amount', parseInt(value) || 0)}
                      autoComplete="off"
                      min={0}
                      max={settings.referredReward.type === "discount" ? 100 : undefined}
                    />
                  </FormLayout>
                </form>
              </BlockStack>
            </Card>

            <Card>
              <BlockStack gap="400">
                <Text as="h2" variant="headingMd">{t('admin.referrals.conditionsTitle')}</Text>
                <form onSubmit={handleSubmit}>
                  <FormLayout>
                    <TextField
                      label={t('admin.referrals.minPurchaseLabel')}
                      type="number"
                      name="minimumPurchase"
                      value={settings.minimumPurchase.toString()}
                      onChange={(value) => updateSetting('minimumPurchase', parseInt(value) || 0)}
                      autoComplete="off"
                      helpText={t('admin.referrals.minPurchaseHelp')}
                      min={0}
                    />
                    <TextField
                      label={t('admin.referrals.expiryDaysLabel')}
                      type="number"
                      name="expiryDays"
                      value={settings.expiryDays.toString()}
                      onChange={(value) => updateSetting('expiryDays', parseInt(value) || 0)}
                      autoComplete="off"
                      helpText={t('admin.referrals.expiryDaysHelp')}
                      min={0}
                    />
                  </FormLayout>
                </form>
              </BlockStack>
            </Card>

            <Card>
              <BlockStack gap="400">
                <Text as="h2" variant="headingMd">{t('admin.referrals.customizationTitle')}</Text>
                <form onSubmit={handleSubmit}>
                  <FormLayout>
                    <TextField
                      label={t('admin.referrals.customMessageLabel')}
                      name="customMessage"
                      value={settings.customMessage}
                      onChange={(value) => updateSetting('customMessage', value)}
                      autoComplete="off"
                      multiline={3}
                      helpText={t('admin.referrals.customMessageHelp')}
                    />
                    <Button submit variant="primary">{t('admin.referrals.save')}</Button>
                  </FormLayout>
                </form>
              </BlockStack>
            </Card>

            {/* Section de test et gestion des clients */}
            <Divider />

            <Card>
              <BlockStack gap="400">
                <Text as="h2" variant="headingMd">{t('admin.referrals.referralLinksTitle')}</Text>
                <Text as="p" variant="bodyMd" tone="subdued">
                  {t('admin.referrals.referralLinksDescription')}
                </Text>

                {customers.length > 0 ? (
                  <DataTable
                    columnContentTypes={['text', 'text', 'text', 'numeric', 'text', 'text']}
                    headings={[
                      t('admin.referrals.customerTableName'),
                      t('admin.referrals.customerTableEmail'),
                      t('admin.referrals.customerTableType'),
                      t('admin.referrals.customerTablePoints'),
                      t('admin.referrals.customerTableStatus'),
                      t('admin.referrals.customerTableAction'),
                    ]}
                    rows={customers.map(customer => [
                      `${customer.firstName || ''} ${customer.lastName || ''}`.trim() || 'N/A',
                      customer.email || 'N/A',
                      customer.type === 'member' ? <Badge tone="success">{t('admin.referrals.member')}</Badge> : <Badge tone="info">{t('admin.referrals.guest')}</Badge>,
                      customer.points.toString(),
                      customer.hasReferralLink ? <Badge tone="success">{t('admin.referrals.linkActive')}</Badge> : <Badge tone="attention">{t('admin.referrals.noLink')}</Badge>,
                      <Button
                        size="slim"
                        onClick={() => handleGenerateReferral(customer.customerId)}
                        disabled={customer.hasReferralLink}
                      >
                        {customer.hasReferralLink ? t('admin.referrals.existingLink') : t('admin.referrals.generateLink')}
                      </Button>
                    ])}
                  />
                ) : (
                  <Text as="p" variant="bodyMd" tone="subdued">
                    {t('admin.referrals.noCustomers')}
                  </Text>
                )}
              </BlockStack>
            </Card>

            <Card>
              <BlockStack gap="400">
                <Text as="h2" variant="headingMd">{t('admin.referrals.linkFormatTitle')}</Text>
                <Text as="p" variant="bodyMd">{t('admin.referrals.linkFormatDescription')}</Text>
                <Text as="p" variant="bodyMd" fontWeight="bold">
                  {t('admin.referrals.linkFormatExample')}
                </Text>
                <Text as="p" variant="bodyMd" tone="subdued">
                  {t('admin.referrals.linkFormatHelp')}
                </Text>
                <Text as="p" variant="bodyMd" tone="subdued">
                  <strong>{t('admin.referrals.linkHowItWorks')}</strong>
                </Text>
                <List type="number">
                  <List.Item>{t('admin.referrals.linkStep1')}</List.Item>
                  <List.Item>{t('admin.referrals.linkStep2')}</List.Item>
                  <List.Item>{t('admin.referrals.linkStep3')}</List.Item>
                  <List.Item>{t('admin.referrals.linkStep4')}</List.Item>
                  <List.Item>{t('admin.referrals.linkStep5')}</List.Item>
                </List>
              </BlockStack>
            </Card>
          </BlockStack>
        </Layout.Section>

        <Layout.Section variant="oneThird">
          <BlockStack gap="500">
            <Card>
              <BlockStack gap="400">
                <Text as="h2" variant="headingMd">{t('admin.referrals.statsTitle')}</Text>
                <InlineStack gap="400">
                  <div>
                    <Text as="p" variant="bodyMd" tone="subdued">{t('admin.referrals.statsTotal')}</Text>
                    <Text as="p" variant="headingLg">{referralStats.total}</Text>
                  </div>
                  <div>
                    <Text as="p" variant="bodyMd" tone="subdued">{t('admin.referrals.statsCompleted')}</Text>
                    <Text as="p" variant="headingLg">{referralStats.completed}</Text>
                  </div>
                  <div>
                    <Text as="p" variant="bodyMd" tone="subdued">{t('admin.referrals.statsPending')}</Text>
                    <Text as="p" variant="headingLg">{referralStats.pending}</Text>
                  </div>
                  <div>
                    <Text as="p" variant="bodyMd" tone="subdued">{t('admin.referrals.statsConversion')}</Text>
                    <Text as="p" variant="headingLg">{referralStats.conversionRate.toFixed(1)}%</Text>
                  </div>
                </InlineStack>
              </BlockStack>
            </Card>

            <Card>
              <BlockStack gap="400">
                <Text as="h2" variant="headingMd">{t('admin.referrals.helpTitle')}</Text>
                <Text as="p" variant="bodyMd">{t('admin.referrals.helpDescription1')}</Text>
                <Text as="p" variant="bodyMd">{t('admin.referrals.helpDescription2')}</Text>
              </BlockStack>
            </Card>
          </BlockStack>
        </Layout.Section>
      </Layout>
      {toastMarkup}
    </AdminLayout>
    </Frame>
  );
}
