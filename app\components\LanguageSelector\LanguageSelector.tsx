import { useState, useCallback } from 'react';
import { Popover, ActionList, Text, InlineStack } from '@shopify/polaris';
import { useTranslation, AVAILABLE_LANGUAGES } from '../../hooks/useTranslation';
import styles from './LanguageSelector.module.css';

export function LanguageSelector() {
  const { currentLocale, setLocale, getCurrentLanguageInfo, t } = useTranslation();
  const [popoverActive, setPopoverActive] = useState(false);
  const [showNotification, setShowNotification] = useState(false);

  const togglePopoverActive = useCallback(
    () => setPopoverActive((popoverActive) => !popoverActive),
    [],
  );

  const handleLanguageSelect = useCallback((languageCode: string) => {
    const selectedLanguage = AVAILABLE_LANGUAGES.find(lang => lang.code === languageCode);
    if (selectedLanguage) {
      setLocale(languageCode as any);
      setPopoverActive(false);

      // Afficher une notification simple
      setShowNotification(true);
      setTimeout(() => setShowNotification(false), 3000);

      // Forcer le re-render en déclenchant un événement personnalisé
      window.dispatchEvent(new CustomEvent('languageChanged', { detail: { locale: languageCode } }));
    }
  }, [setLocale]);

  const currentLanguage = getCurrentLanguageInfo();

  const activator = (
    <div
      style={{
        background: 'rgba(255, 255, 255, 0.15)',
        border: '1px solid rgba(255, 255, 255, 0.3)',
        borderRadius: '6px',
        padding: '6px 12px',
        cursor: 'pointer',
        transition: 'all 0.2s ease',
        color: 'white',
        display: 'flex',
        alignItems: 'center',
        gap: '8px',
        fontSize: '14px',
        fontWeight: '500',
        minHeight: 'auto',
        height: '30px',
        margin: '0 8px',
      }}
      onClick={togglePopoverActive}
      role="button"
      tabIndex={0}
      onKeyDown={(e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          togglePopoverActive();
        }
      }}
      onMouseEnter={(e) => {
        e.currentTarget.style.background = 'rgba(255, 255, 255, 0.25)';
        e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.5)';
      }}
      onMouseLeave={(e) => {
        e.currentTarget.style.background = 'rgba(255, 255, 255, 0.15)';
        e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.3)';
      }}
    >
      <span style={{ fontSize: '16px' }}>
        {currentLanguage.flag}
      </span>
      <span style={{ color: 'white', fontWeight: '500' }}>
        {currentLanguage.name}
      </span>
      <span style={{ color: 'white', fontSize: '12px', marginLeft: '4px' }}>
        ▼
      </span>
    </div>
  );

  const languageActions = AVAILABLE_LANGUAGES.map((language) => ({
    content: `${language.flag} ${language.name}${language.code === currentLocale ? ' ✓' : ''}`,
    onAction: () => handleLanguageSelect(language.code),
    active: language.code === currentLocale,
  }));

  return (
    <div className={styles.languageSelector} style={{ position: 'relative' }}>
      {showNotification && (
        <div
          style={{
            position: 'fixed',
            top: '20px',
            right: '20px',
            background: '#28a745',
            color: 'white',
            padding: '12px 20px',
            borderRadius: '6px',
            boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
            zIndex: 10000,
            fontSize: '14px',
            fontWeight: '500',
            animation: 'slideIn 0.3s ease-out'
          }}
        >
          {t('admin.notifications.languageChanged', { language: getCurrentLanguageInfo().name })}
        </div>
      )}
      <Popover
        active={popoverActive}
        activator={activator}
        autofocusTarget="first-node"
        onClose={togglePopoverActive}
        preferredAlignment="right"
      >
        <ActionList
          actionRole="menuitem"
          items={languageActions}
        />
      </Popover>
      <style>{`
        @keyframes slideIn {
          from {
            transform: translateX(100%);
            opacity: 0;
          }
          to {
            transform: translateX(0);
            opacity: 1;
          }
        }
      `}</style>
    </div>
  );
}
