import { json, type ActionFunctionArgs, type LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData, useSubmit, useNavigate } from "@remix-run/react";
import { authenticate } from "../shopify.server";
import { getSiteSettings, upsertSiteSettings, getDefaultSiteSettings } from "../models/SiteSettings.server";
import {
  Card,
  FormLayout,
  TextField,
  Button,
  Select,
  BlockStack,
  Text,
  Checkbox,
  Toast,
  Frame,
  InlineStack,
  Page,
  Layout,
} from "@shopify/polaris";
import { AdminLayout } from "../components/Layout/AdminLayout";
import { useState, useCallback } from "react";
import { useTranslation } from "../hooks/useTranslation";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const { session } = await authenticate.admin(request);
  const shop = session.shop;

  try {
    // Récupérer les paramètres du site existants
    let settings = await getSiteSettings(shop);

    // Si aucun paramètre n'existe, utiliser les valeurs par défaut
    if (!settings) {
      settings = getDefaultSiteSettings(shop);
    }

    return json({ settings });
  } catch (error) {
    console.error("Error loading site settings:", error);
    // En cas d'erreur, retourner les paramètres par défaut
    return json({ settings: getDefaultSiteSettings(shop) });
  }
};

export const action = async ({ request }: ActionFunctionArgs) => {
  const { session } = await authenticate.admin(request);
  const shop = session.shop;

  const formData = await request.formData();

  try {
    // Extraire les données du formulaire
    const settingsData = {
      shopName: formData.get("shopName") as string,
      currency: formData.get("currency") as string,
      language: formData.get("language") as string,
      emailNotifications: formData.get("emailNotifications") === "true",
      pointsName: formData.get("pointsName") as string,
      welcomeMessage: formData.get("welcomeMessage") as string,
    };

    // Sauvegarder les paramètres
    await upsertSiteSettings(shop, settingsData);

    return json({ success: true, message: "Paramètres sauvegardés avec succès" });
  } catch (error) {
    console.error("Error saving site settings:", error);
    return json({ error: "Erreur lors de la sauvegarde" }, { status: 500 });
  }
};

export default function AdminSettings() {
  const { settings: initialSettings } = useLoaderData<typeof loader>();
  const submit = useSubmit();
  const navigate = useNavigate();
  const { t } = useTranslation();

  // État local pour tous les paramètres
  const [settings, setSettings] = useState(initialSettings);
  const [emailEnabled, setEmailEnabled] = useState(initialSettings.emailNotifications);
  const [hasChanges, setHasChanges] = useState(false);
  const [toastActive, setToastActive] = useState(false);
  const [toastMessage, setToastMessage] = useState("");

  const handleSubmit = (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    const formData = new FormData();

    // Ajouter tous les paramètres au FormData
    Object.entries(settings).forEach(([key, value]) => {
      formData.append(key, value.toString());
    });

    submit(formData, { method: "post" });

    // Attendre la réponse pour confirmer la sauvegarde
    setTimeout(() => {
      setToastMessage(t("admin.settings.saveSuccess"));
      setToastActive(true);
      setHasChanges(false); // Reset seulement après confirmation de sauvegarde
    }, 1000);
  };

  // Handler pour mettre à jour les paramètres
  const updateSetting = useCallback((field: keyof typeof initialSettings, value: string) => {
    setSettings(prev => ({
      ...prev,
      [field]: value
    }));
    setHasChanges(true);
  }, [initialSettings]);

  const toggleToastActive = useCallback(() => setToastActive((active) => !active), []);

  const toastMarkup = toastActive ? (
    <Toast content={toastMessage} onDismiss={toggleToastActive} />
  ) : null;

  const languageOptions = [
    { label: "Français", value: "fr" },
    { label: "English", value: "en" },
    { label: "Español", value: "es" },
  ];

  return (
    <Frame>
      <AdminLayout title={t("admin.settings.title")}>
        <Page fullWidth>
          <Layout>
            <Layout.Section>
              <Card>
                <BlockStack gap="400">
                  <Text as="h2" variant="headingMd">
                    {t("admin.settings.quickNavigation")}
                  </Text>

                  <InlineStack gap="300">
                    <Button
                      variant="primary"
                      onClick={() => navigate('/app/settings/widget')}
                    >
                      {t("admin.settings.customizeWidget")}
                    </Button>

                    <Button
                      onClick={() => navigate('/app/settings/products')}
                    >
                      {t("admin.settings.exchangeableProducts")}
                    </Button>
                  </InlineStack>
                </BlockStack>
              </Card>
            </Layout.Section>

            <Layout.Section>
              <form onSubmit={handleSubmit}>
                <BlockStack gap="500">
                  <Card>
                    <BlockStack gap="400">
                      <Text as="h2" variant="headingMd">{t("admin.settings.generalSettings")}</Text>
                      <FormLayout>
                        <TextField
                          label={t("admin.settings.shopName")}
                          name="shopName"
                          value={settings.shopName}
                          onChange={(value) => updateSetting('shopName', value)}
                          autoComplete="off"
                        />
                        <Select
                          label={t("admin.settings.language")}
                          options={languageOptions}
                          name="language"
                          value={settings.language}
                          onChange={(value) => updateSetting('language', value)}
                        />
                        <TextField
                          label={t("admin.settings.currency")}
                          name="currency"
                          value={settings.currency}
                          onChange={(value) => updateSetting('currency', value)}
                          autoComplete="off"
                        />
                      </FormLayout>
                    </BlockStack>
                  </Card>

                  <Card>
                    <BlockStack gap="400">
                      <Text as="h2" variant="headingMd">{t("admin.settings.customizationTitle")}</Text>
                      <FormLayout>
                        <TextField
                          label={t("admin.settings.pointsName")}
                          name="pointsName"
                          value={settings.pointsName}
                          onChange={(value) => updateSetting('pointsName', value)}
                          autoComplete="off"
                          helpText={t("admin.settings.pointsNameHelp")}
                        />
                        <TextField
                          label={t("admin.settings.welcomeMessage")}
                          name="welcomeMessage"
                          value={settings.welcomeMessage}
                          onChange={(value) => updateSetting('welcomeMessage', value)}
                          autoComplete="off"
                          multiline={3}
                          helpText={t("admin.settings.welcomeMessageHelp")}
                        />
                      </FormLayout>
                    </BlockStack>
                  </Card>

                  <Card>
                    <BlockStack gap="400">
                      <Text as="h2" variant="headingMd">{t("admin.settings.notificationsTitle")}</Text>
                      <FormLayout>
                        <Checkbox
                          label={t("admin.settings.emailNotifications")}
                          checked={emailEnabled}
                          onChange={setEmailEnabled}
                        />
                        {emailEnabled && (
                          <TextField
                            label={t("admin.settings.senderEmail")}
                            name="senderEmail"
                            type="email"
                            autoComplete="off"
                          />
                        )}
                      </FormLayout>
                    </BlockStack>
                  </Card>

                  {/* Bouton de sauvegarde global */}
                  <Card>
                    <BlockStack gap="400">
                      <Text as="h2" variant="headingMd">{t("admin.settings.saveCardTitle")}</Text>
                      <Button
                        submit
                        variant="primary"
                        size="large"
                        disabled={!hasChanges}
                      >
                        {hasChanges ? t("admin.settings.saveButton") : t("admin.settings.noChanges")}
                      </Button>
                      {hasChanges && (
                        <Text as="p" variant="bodySm" tone="subdued">
                          {t("admin.settings.unsavedChanges")}
                        </Text>
                      )}
                    </BlockStack>
                  </Card>
                </BlockStack>
              </form>
            </Layout.Section>
          </Layout>
          {toastMarkup}
        </Page>
      </AdminLayout>
    </Frame>
  );
}
