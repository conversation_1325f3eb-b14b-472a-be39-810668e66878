import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import { authenticate } from "../shopify.server";
import prisma from "../db.server";
import { AdminLayout } from "../components/Layout/AdminLayout";
import { Card, Text, BlockStack } from "@shopify/polaris";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const { session } = await authenticate.admin(request);
  const shop = session.shop;

  try {
    // Vérifier les données existantes
    const customers = await prisma.customer.findMany({
      where: { shop },
      take: 5
    });

    const pointsHistory = await prisma.pointsHistory.findMany({
      where: {
        customer: { shop }
      },
      take: 5,
      include: {
        customer: true
      }
    });

    const referrals = await prisma.referral.findMany({
      where: { shop },
      take: 5
    });

    const rewards = await prisma.reward.findMany({
      where: { shop },
      take: 5
    });

    // Compter les totaux
    const customerCount = await prisma.customer.count({ where: { shop } });
    const pointsHistoryCount = await prisma.pointsHistory.count({
      where: { customer: { shop } }
    });
    const referralCount = await prisma.referral.count({ where: { shop } });
    const rewardCount = await prisma.reward.count({ where: { shop } });

    return json({
      shop,
      customers,
      pointsHistory,
      referrals,
      rewards,
      counts: {
        customers: customerCount,
        pointsHistory: pointsHistoryCount,
        referrals: referralCount,
        rewards: rewardCount
      }
    });
  } catch (error) {
    console.error("Debug error:", error);
    return json({
      error: error.message,
      shop,
      customers: [],
      pointsHistory: [],
      referrals: [],
      rewards: [],
      counts: { customers: 0, pointsHistory: 0, referrals: 0, rewards: 0 }
    });
  }
};

export default function Debug() {
  const data = useLoaderData<typeof loader>();

  return (
    <AdminLayout title="Debug - Données de la base">
      <BlockStack gap="400">
        <Card>
          <BlockStack gap="200">
            <Text as="h2" variant="headingMd">Shop: {data.shop}</Text>
            {data.error && (
              <Text as="p" tone="critical">Erreur: {data.error}</Text>
            )}
          </BlockStack>
        </Card>

        <Card>
          <BlockStack gap="200">
            <Text as="h3" variant="headingMd">Compteurs</Text>
            <Text as="p">Clients: {data.counts.customers}</Text>
            <Text as="p">Historique des points: {data.counts.pointsHistory}</Text>
            <Text as="p">Parrainages: {data.counts.referrals}</Text>
            <Text as="p">Récompenses: {data.counts.rewards}</Text>
          </BlockStack>
        </Card>

        <Card>
          <BlockStack gap="200">
            <Text as="h3" variant="headingMd">Clients (5 premiers)</Text>
            {data.customers.map((customer: any) => (
              <Text key={customer.id} as="p">
                {customer.firstName} {customer.lastName} - {customer.points} points
              </Text>
            ))}
          </BlockStack>
        </Card>

        <Card>
          <BlockStack gap="200">
            <Text as="h3" variant="headingMd">Historique des points (5 premiers)</Text>
            {data.pointsHistory.map((history: any) => (
              <Text key={history.id} as="p">
                {history.customer?.firstName} {history.customer?.lastName} - {history.action}: {history.points} points
              </Text>
            ))}
          </BlockStack>
        </Card>

        <Card>
          <BlockStack gap="200">
            <Text as="h3" variant="headingMd">Parrainages (5 premiers)</Text>
            {data.referrals.map((referral: any) => (
              <Text key={referral.id} as="p">
                Code: {referral.code} - Status: {referral.status}
              </Text>
            ))}
          </BlockStack>
        </Card>

        <Card>
          <BlockStack gap="200">
            <Text as="h3" variant="headingMd">Récompenses (5 premières)</Text>
            {data.rewards.map((reward: any) => (
              <Text key={reward.id} as="p">
                {reward.name} - {reward.type}
              </Text>
            ))}
          </BlockStack>
        </Card>
      </BlockStack>
    </AdminLayout>
  );
}
