import { DeliveryMethod } from "@shopify/shopify-api";
import { upsertCustomerFromShopify } from "../models/Customer.server";

/**
 * Webhook pour synchroniser les clients Shopify avec notre base de données
 */

export const customersCreate = {
  deliveryMethod: DeliveryMethod.Http,
  callbackUrl: "/webhooks/customers/create",
  callback: async (topic: string, shop: string, body: string) => {
    console.log(`Webhook reçu: ${topic} pour ${shop}`);
    
    try {
      const customer = JSON.parse(body);
      
      // Créer ou mettre à jour le client dans notre base de données
      const result = await upsertCustomerFromShopify(customer, shop);
      
      if (result) {
        console.log(`Client ${customer.id} synchronisé avec succès`);
      } else {
        console.error(`Erreur lors de la synchronisation du client ${customer.id}`);
      }
    } catch (error) {
      console.error("Erreur lors du traitement du webhook customers/create:", error);
    }
  },
};

export const customersUpdate = {
  deliveryMethod: DeliveryMethod.Http,
  callbackUrl: "/webhooks/customers/update",
  callback: async (topic: string, shop: string, body: string) => {
    console.log(`Webhook reçu: ${topic} pour ${shop}`);
    
    try {
      const customer = JSON.parse(body);
      
      // Mettre à jour le client dans notre base de données
      const result = await upsertCustomerFromShopify(customer, shop);
      
      if (result) {
        console.log(`Client ${customer.id} mis à jour avec succès`);
      } else {
        console.error(`Erreur lors de la mise à jour du client ${customer.id}`);
      }
    } catch (error) {
      console.error("Erreur lors du traitement du webhook customers/update:", error);
    }
  },
};

export const customersDelete = {
  deliveryMethod: DeliveryMethod.Http,
  callbackUrl: "/webhooks/customers/delete",
  callback: async (topic: string, shop: string, body: string) => {
    console.log(`Webhook reçu: ${topic} pour ${shop}`);
    
    try {
      const customer = JSON.parse(body);
      
      // Note: En général, on ne supprime pas les données client pour des raisons légales
      // On peut marquer le client comme supprimé ou anonymiser les données
      console.log(`Client ${customer.id} supprimé de Shopify`);
      
      // TODO: Implémenter la logique de suppression/anonymisation si nécessaire
      
    } catch (error) {
      console.error("Erreur lors du traitement du webhook customers/delete:", error);
    }
  },
};
