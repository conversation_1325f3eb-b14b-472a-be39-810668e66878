import { useState, useCallback, useEffect } from "react";
import {
  Modal,
  FormLayout,
  TextField,
  Button,
  Select,
  Checkbox,
  BlockStack,
  Text
} from "@shopify/polaris";

interface WayToRedeemModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (data: WayToRedeemFormData) => void;
  wayToRedeem?: any;
  isLoading?: boolean;
}

export interface WayToRedeemFormData {
  name: string;
  description: string;
  redeemType: "discount" | "product" | "shipping" | "coupon";
  redeemValue: string;
  pointsCost: string;
  icon: string;
  isActive: boolean;
  isConfigurable: boolean;
  minPoints: string;
  maxPoints: string;
  minValue: string;
  maxValue: string;
  expiryDays: string;
  usageLimit: string;
}

export function WayToRedeemModal({ isOpen, onClose, onSave, wayToRedeem, isLoading = false }: WayToRedeemModalProps) {
  const [formState, setFormState] = useState<WayToRedeemFormData>({
    name: wayToRedeem?.name || "",
    description: wayToRedeem?.description || "",
    redeemType: wayToRedeem?.redeemType || "discount",
    redeemValue: wayToRedeem?.redeemValue?.toString() || "",
    pointsCost: wayToRedeem?.pointsCost?.toString() || "",
    icon: wayToRedeem?.icon || "discount",
    isActive: wayToRedeem?.isActive ?? true,
    isConfigurable: wayToRedeem?.isConfigurable ?? false,
    minPoints: wayToRedeem?.minPoints?.toString() || "",
    maxPoints: wayToRedeem?.maxPoints?.toString() || "",
    minValue: wayToRedeem?.minValue?.toString() || "",
    maxValue: wayToRedeem?.maxValue?.toString() || "",
    expiryDays: wayToRedeem?.expiryDays?.toString() || "30",
    usageLimit: wayToRedeem?.usageLimit?.toString() || "1"
  });

  const [errors, setErrors] = useState<Partial<WayToRedeemFormData>>({});

  // Mettre à jour le formulaire quand wayToRedeem change
  useEffect(() => {
    if (wayToRedeem) {
      setFormState({
        name: wayToRedeem.name || "",
        description: wayToRedeem.description || "",
        redeemType: wayToRedeem.redeemType || "discount",
        redeemValue: wayToRedeem.redeemValue?.toString() || "",
        pointsCost: wayToRedeem.pointsCost?.toString() || "",
        icon: wayToRedeem.icon || "discount",
        isActive: wayToRedeem.isActive ?? true,
        isConfigurable: wayToRedeem.isConfigurable ?? false,
        minPoints: wayToRedeem.minPoints?.toString() || "",
        maxPoints: wayToRedeem.maxPoints?.toString() || "",
        minValue: wayToRedeem.minValue?.toString() || "",
        maxValue: wayToRedeem.maxValue?.toString() || "",
        expiryDays: wayToRedeem.expiryDays?.toString() || "30",
        usageLimit: wayToRedeem.usageLimit?.toString() || "1"
      });
    } else {
      setFormState({
        name: "",
        description: "",
        redeemType: "discount",
        redeemValue: "",
        pointsCost: "",
        icon: "discount",
        isActive: true,
        isConfigurable: false,
        minPoints: "",
        maxPoints: "",
        minValue: "",
        maxValue: "",
        expiryDays: "30",
        usageLimit: "1"
      });
    }
    setErrors({});
  }, [wayToRedeem, isOpen]);

  const handleSubmit = useCallback(() => {
    // Validation
    const newErrors: Partial<WayToRedeemFormData> = {};

    if (!formState.name.trim()) {
      newErrors.name = "Le nom est requis";
    }

    if (!formState.description.trim()) {
      newErrors.description = "La description est requise";
    }

    // Validation conditionnelle selon le type et la configuration
    if (formState.redeemType === "coupon" && formState.isConfigurable) {
      // Pour les coupons configurables, valider les limites
      if (formState.minPoints && formState.maxPoints) {
        const minPts = parseInt(formState.minPoints);
        const maxPts = parseInt(formState.maxPoints);
        if (minPts >= maxPts) {
          newErrors.maxPoints = "Le maximum doit être supérieur au minimum";
        }
      }
      if (formState.minValue && formState.maxValue) {
        const minVal = parseFloat(formState.minValue);
        const maxVal = parseFloat(formState.maxValue);
        if (minVal >= maxVal) {
          newErrors.maxValue = "La valeur maximum doit être supérieure au minimum";
        }
      }
    } else {
      // Pour les types fixes, valider les valeurs normalement
      if (!formState.redeemValue || parseFloat(formState.redeemValue) <= 0) {
        newErrors.redeemValue = "La valeur doit être positive";
      }
      if (!formState.pointsCost || parseInt(formState.pointsCost) <= 0) {
        newErrors.pointsCost = "Le coût en points doit être positif";
      }
    }

    setErrors(newErrors);

    if (Object.keys(newErrors).length === 0) {
      // Pour les coupons configurables, définir des valeurs par défaut
      const finalFormState = { ...formState };
      if (formState.redeemType === "coupon" && formState.isConfigurable) {
        // Utiliser les valeurs minimum comme valeurs par défaut pour l'affichage
        finalFormState.pointsCost = formState.minPoints || "100";
        finalFormState.redeemValue = formState.minValue || "1";
      }
      onSave(finalFormState);
    }
  }, [formState, onSave]);

  const handleClose = useCallback(() => {
    setFormState({
      name: "",
      description: "",
      redeemType: "discount",
      redeemValue: "",
      pointsCost: "",
      icon: "discount",
      isActive: true,
      isConfigurable: false,
      minPoints: "",
      maxPoints: "",
      minValue: "",
      maxValue: "",
      expiryDays: "30",
      usageLimit: "1"
    });
    setErrors({});
    onClose();
  }, [onClose]);

  const iconOptions = [
    { label: "Réduction", value: "discount" },
    { label: "Produit gratuit", value: "product" },
    { label: "Livraison gratuite", value: "shipping" },
    { label: "Cadeau", value: "gift" },
    { label: "Bon d'achat", value: "voucher" }
  ];

  const redeemTypeOptions = [
    { label: "Réduction sur commande", value: "discount" },
    { label: "Produit gratuit", value: "product" },
    { label: "Livraison gratuite", value: "shipping" },
    { label: "Coupon de réduction", value: "coupon" }
  ];

  const getRedeemValueLabel = () => {
    switch (formState.redeemType) {
      case "discount":
        return "Montant de la réduction (€)";
      case "product":
        return "Valeur du produit (€)";
      case "shipping":
        return "Valeur de la livraison (€)";
      case "coupon":
        return formState.isConfigurable ? "Valeur par défaut (€)" : "Valeur du coupon (€)";
      default:
        return "Valeur (€)";
    }
  };

  return (
    <Modal
      open={isOpen}
      onClose={handleClose}
      title={wayToRedeem ? "Modifier la façon d'échanger des points" : "Ajouter une façon d'échanger des points"}
      primaryAction={{
        content: wayToRedeem ? "Enregistrer" : "Créer",
        onAction: handleSubmit,
        loading: isLoading
      }}
      secondaryActions={[
        {
          content: "Annuler",
          onAction: handleClose
        }
      ]}
    >
      <Modal.Section>
        <FormLayout>
          <TextField
            label="Nom"
            value={formState.name}
            onChange={(value) => setFormState(prev => ({ ...prev, name: value }))}
            placeholder="ex: Réduction sur commande"
            error={errors.name}
            autoComplete="off"
            requiredIndicator
          />

          <TextField
            label="Description"
            value={formState.description}
            onChange={(value) => setFormState(prev => ({ ...prev, description: value }))}
            placeholder="ex: Obtenez une réduction sur vos commandes"
            error={errors.description}
            autoComplete="off"
            requiredIndicator
          />

          <Select
            label="Type d'échange"
            options={redeemTypeOptions}
            value={formState.redeemType}
            onChange={(value) => setFormState(prev => ({ ...prev, redeemType: value as "discount" | "product" | "shipping" }))}
          />

          {formState.redeemType === "coupon" && (
            <Checkbox
              label="Coupon configurable"
              checked={formState.isConfigurable}
              onChange={(checked) => setFormState(prev => ({ ...prev, isConfigurable: checked }))}
              helpText="Si activé, les clients pourront choisir le montant de points à échanger"
            />
          )}

          {(!formState.isConfigurable || formState.redeemType !== "coupon") && (
            <>
              <TextField
                label={getRedeemValueLabel()}
                value={formState.redeemValue}
                onChange={(value) => setFormState(prev => ({ ...prev, redeemValue: value }))}
                type="number"
                step={0.01}
                min={0}
                error={errors.redeemValue}
                autoComplete="off"
                requiredIndicator
              />

              <TextField
                label="Coût en points"
                value={formState.pointsCost}
                onChange={(value) => setFormState(prev => ({ ...prev, pointsCost: value }))}
                type="number"
                min={1}
                error={errors.pointsCost}
                autoComplete="off"
                requiredIndicator
              />
            </>
          )}

          {formState.redeemType === "coupon" && formState.isConfigurable && (
            <>
              <Text as="h3" variant="headingSm">Limites de configuration</Text>

              <FormLayout.Group>
                <TextField
                  label="Points minimum"
                  value={formState.minPoints}
                  onChange={(value) => setFormState(prev => ({ ...prev, minPoints: value }))}
                  type="number"
                  min={1}
                  error={errors.minPoints}
                  autoComplete="off"
                />
                <TextField
                  label="Points maximum"
                  value={formState.maxPoints}
                  onChange={(value) => setFormState(prev => ({ ...prev, maxPoints: value }))}
                  type="number"
                  min={1}
                  error={errors.maxPoints}
                  autoComplete="off"
                />
              </FormLayout.Group>

              <FormLayout.Group>
                <TextField
                  label="Valeur minimum (€)"
                  value={formState.minValue}
                  onChange={(value) => setFormState(prev => ({ ...prev, minValue: value }))}
                  type="number"
                  step={0.01}
                  min={0}
                  error={errors.minValue}
                  autoComplete="off"
                />
                <TextField
                  label="Valeur maximum (€)"
                  value={formState.maxValue}
                  onChange={(value) => setFormState(prev => ({ ...prev, maxValue: value }))}
                  type="number"
                  step={0.01}
                  min={0}
                  error={errors.maxValue}
                  autoComplete="off"
                />
              </FormLayout.Group>
            </>
          )}

          {formState.redeemType === "coupon" && (
            <FormLayout.Group>
              <TextField
                label="Durée de validité (jours)"
                value={formState.expiryDays}
                onChange={(value) => setFormState(prev => ({ ...prev, expiryDays: value }))}
                type="number"
                min={1}
                autoComplete="off"
                helpText="Nombre de jours avant expiration du coupon"
              />
              <TextField
                label="Nombre d'utilisations"
                value={formState.usageLimit}
                onChange={(value) => setFormState(prev => ({ ...prev, usageLimit: value }))}
                type="number"
                min={1}
                autoComplete="off"
                helpText="Nombre de fois que le coupon peut être utilisé"
              />
            </FormLayout.Group>
          )}

          <Select
            label="Icône"
            options={iconOptions}
            value={formState.icon}
            onChange={(value) => setFormState(prev => ({ ...prev, icon: value }))}
          />

          <Checkbox
            label="Actif"
            checked={formState.isActive}
            onChange={(checked) => setFormState(prev => ({ ...prev, isActive: checked }))}
          />
        </FormLayout>
      </Modal.Section>
    </Modal>
  );
}
