import prisma from "../db.server";
import { getDefaultSiteSettings, getSiteSettings } from "./SiteSettings.server";
import { getPointsSettings } from "./PointsSettings.server";

export interface ExchangeableProduct {
  id: string;
  shopifyProductId: string;
  title: string;
  handle: string;
  image?: string;
  price: string;
  pointsCost: number;
  active: boolean;
}

/**
 * Récupérer les produits échangeables pour un shop
 */
export async function getExchangeableProducts(shop: string): Promise<ExchangeableProduct[]> {
  try {
    // Pour l'instant, on utilise une table générique Settings pour stocker les produits
    // Dans un vrai projet, vous pourriez créer une table dédiée ExchangeableProducts
    const settings = await prisma.settings.findUnique({
      where: { shop },
      select: { exchangeableProducts: true }
    });

    if (!settings?.exchangeableProducts) {
      return [];
    }

    // Les produits sont stockés en JSON dans la colonne exchangeableProducts
    const products = JSON.parse(settings.exchangeableProducts);
    return Array.isArray(products) ? products : [];
  } catch (error) {
    console.error("Error fetching exchangeable products:", error);
    return [];
  }
}

/**
 * Sauvegarder les produits échangeables
 */
export async function saveExchangeableProducts(
  shop: string,
  products: ExchangeableProduct[]
): Promise<boolean> {
  try {
    // Récupérer les paramètres existants du programme depuis la base de données
    const [existingPointsSettings, existingSiteSettings] = await Promise.all([
      getPointsSettings(shop),
      getSiteSettings(shop)
    ]);

    // Utiliser les paramètres existants ou les valeurs par défaut
    const defaultSiteSettings = existingSiteSettings || getDefaultSiteSettings(shop);

    await prisma.settings.upsert({
      where: { shop },
      update: {
        exchangeableProducts: JSON.stringify(products)
      },
      create: {
        shop,
        // Utiliser les paramètres existants ou les valeurs par défaut du schéma Prisma
        earningRate: existingPointsSettings?.earningRate ?? 1.0,
        redemptionRate: existingPointsSettings?.redemptionRate ?? 0.01,
        minimumPoints: existingPointsSettings?.minimumPoints ?? 100,
        expirationDays: existingPointsSettings?.expirationDays ?? 365,
        referralPoints: existingPointsSettings?.referralPoints ?? 100,
        birthdayPoints: existingPointsSettings?.birthdayPoints ?? 250,
        widgetEnabled: true,
        primaryColor: defaultSiteSettings.widgetColor,
        widgetSecondaryColor: defaultSiteSettings.widgetSecondaryColor,
        widgetTextColor: defaultSiteSettings.widgetTextColor,
        widgetPosition: defaultSiteSettings.widgetPosition,
        widgetSize: defaultSiteSettings.widgetSize,
        widgetBorderRadius: defaultSiteSettings.widgetBorderRadius,
        widgetShadow: defaultSiteSettings.widgetShadow,
        widgetAnimation: defaultSiteSettings.widgetAnimation,
        showPointsOnButton: defaultSiteSettings.showPointsOnButton,
        pointsName: defaultSiteSettings.pointsName,
        welcomeMessage: defaultSiteSettings.welcomeMessage,
        shopName: defaultSiteSettings.shopName,
        currency: defaultSiteSettings.currency,
        emailNotifications: defaultSiteSettings.emailNotifications,
        language: defaultSiteSettings.language,
        customCSS: defaultSiteSettings.customCSS,
        exchangeableProducts: JSON.stringify(products)
      }
    });

    return true;
  } catch (error) {
    console.error("Error saving exchangeable products:", error);
    return false;
  }
}

/**
 * Ajouter un produit aux produits échangeables
 */
export async function addExchangeableProduct(
  shop: string,
  product: Omit<ExchangeableProduct, 'id'>
): Promise<boolean> {
  try {
    const existingProducts = await getExchangeableProducts(shop);

    // Vérifier si le produit existe déjà
    const exists = existingProducts.some(p => p.shopifyProductId === product.shopifyProductId);
    if (exists) {
      return false; // Produit déjà ajouté
    }

    // Ajouter le nouveau produit
    const newProduct: ExchangeableProduct = {
      ...product,
      id: `${Date.now()}-${Math.random().toString(36).substring(2, 11)}`, // ID unique
      active: true
    };

    const updatedProducts = [...existingProducts, newProduct];
    return await saveExchangeableProducts(shop, updatedProducts);
  } catch (error) {
    console.error("Error adding exchangeable product:", error);
    return false;
  }
}

/**
 * Supprimer un produit des produits échangeables
 */
export async function removeExchangeableProduct(
  shop: string,
  productId: string
): Promise<boolean> {
  try {
    const existingProducts = await getExchangeableProducts(shop);
    const updatedProducts = existingProducts.filter(p => p.id !== productId);

    return await saveExchangeableProducts(shop, updatedProducts);
  } catch (error) {
    console.error("Error removing exchangeable product:", error);
    return false;
  }
}

/**
 * Mettre à jour le coût en points d'un produit
 */
export async function updateProductPointsCost(
  shop: string,
  productId: string,
  pointsCost: number
): Promise<boolean> {
  try {
    const existingProducts = await getExchangeableProducts(shop);
    const updatedProducts = existingProducts.map(p =>
      p.id === productId ? { ...p, pointsCost } : p
    );

    return await saveExchangeableProducts(shop, updatedProducts);
  } catch (error) {
    console.error("Error updating product points cost:", error);
    return false;
  }
}

/**
 * Mettre à jour le statut actif/inactif d'un produit
 */
export async function updateProductStatus(
  shop: string,
  productId: string,
  active: boolean
): Promise<boolean> {
  try {
    const existingProducts = await getExchangeableProducts(shop);
    const updatedProducts = existingProducts.map(p =>
      p.id === productId ? { ...p, active } : p
    );

    return await saveExchangeableProducts(shop, updatedProducts);
  } catch (error) {
    console.error("Error updating product status:", error);
    return false;
  }
}

/**
 * Calculer le coût en points basé sur le prix et les paramètres du programme
 */
export async function calculatePointsCost(shop: string, price: string): Promise<number> {
  try {
    // Récupérer les paramètres du programme depuis la table Settings
    const settings = await prisma.settings.findUnique({
      where: { shop },
      select: {
        redemptionRate: true,
        earningRate: true,
        minimumPoints: true
      }
    });

    if (!settings) {
      // Valeurs par défaut si pas de paramètres (100 points = 1€)
      return Math.max(100, Math.ceil(parseFloat(price) * 100));
    }

    // Calculer les points basés sur le taux de conversion
    // redemptionRate = points par euro (ex: 100 points = 1€)
    // donc points = prix × redemptionRate
    const calculatedPoints = Math.ceil(parseFloat(price) * settings.redemptionRate);

    // S'assurer que c'est au moins le minimum requis
    return Math.max(settings.minimumPoints, calculatedPoints);
  } catch (error) {
    console.error("Error calculating points cost:", error);
    // Valeur par défaut en cas d'erreur
    return Math.max(100, Math.ceil(parseFloat(price) * 10));
  }
}

/**
 * Recalculer les points de tous les produits échangeables selon les paramètres actuels
 */
export async function recalculateAllProductsPoints(shop: string): Promise<boolean> {
  try {
    const existingProducts = await getExchangeableProducts(shop);

    // Recalculer les points pour chaque produit
    const updatedProducts = await Promise.all(
      existingProducts.map(async (product) => {
        const newPointsCost = await calculatePointsCost(shop, product.price || '0');
        return {
          ...product,
          pointsCost: newPointsCost
        };
      })
    );

    // Sauvegarder les produits avec les nouveaux points
    return await saveExchangeableProducts(shop, updatedProducts);
  } catch (error) {
    console.error("Error recalculating all products points:", error);
    return false;
  }
}
