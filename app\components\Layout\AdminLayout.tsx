import {
  Frame,
  Navigation,
  Page,
  TopBar,
  Text,
  InlineStack,
  TextField,
} from "@shopify/polaris";
import {
  HomeIcon,
  SettingsIcon,
  CursorIcon,
  GiftCardIcon,
  MarketsIcon,
  OrderIcon,
  EmailIcon,
  StarIcon,
  ProfileIcon,
  ChartLineIcon
} from "@shopify/polaris-icons";
import { useNavigate, useLocation, Link } from "@remix-run/react";
import { useState, useCallback } from "react";
import { useTranslation } from "../../hooks/useTranslation";
import { LanguageSelector } from "../LanguageSelector/LanguageSelector";
import { SearchField } from "../SearchField";
interface AdminLayoutProps {
  children: React.ReactNode;
  title?: string;
}

export function AdminLayout({ children, title = "Administration" }: AdminLayoutProps) {
  const navigate = useNavigate();
  const location = useLocation();
  const [userMenuActive, setUserMenuActive] = useState(false);
  const { t } = useTranslation();

  const toggleUserMenuActive = useCallback(
    () => setUserMenuActive((userMenuActive) => !userMenuActive),
    [],
  );

  const [searchActive, setSearchActive] = useState<boolean>(false);

  const toggleSearchActive: () => void = useCallback(
    () => setSearchActive((searchActive: boolean) => !searchActive),
    [],
  );

  const navigationItems = [
    {
      label: t("admin.navigation.dashboard"),
      icon: HomeIcon,
      url: "/app/admin",
      selected: location.pathname === "/app/admin",
    },
    {
      label: t("admin.navigation.program"),
      icon: StarIcon,
      url: "/app/program",
      selected: location.pathname.startsWith("/app/program"),
      subNavigationItems: [
        {
          label: t("admin.navigation.overview"),
          url: "/app/program",
          selected: location.pathname === "/app/program",
        },
        {
          label: t("admin.navigation.pointsConfig"),
          url: "/app/program/points",
          selected: location.pathname === "/app/program/points",
        },
        {
          label: t("admin.navigation.referrals"),
          url: "/app/program/referrals",
          selected: location.pathname === "/app/program/referrals",
        },
        {
          label: t("admin.navigation.vipProgram"),
          url: "/app/program/vip",
          selected: location.pathname === "/app/program/vip",
        },
        {
          label: t("admin.navigation.bonusCampaigns"),
          url: "/app/program/campaigns",
          selected: location.pathname === "/app/program/campaigns",
        },
      ],
    },
    {
      label: t("admin.navigation.customers"),
      icon: ProfileIcon,
      url: "/app/customers",
      selected: location.pathname === "/app/customers",
    },
    {
      label: t("admin.navigation.analytics"),
      icon: ChartLineIcon,
      url: "/app/analytics",
      selected: location.pathname === "/app/analytics",
    },
  ];

  const secondaryNavItems = [
    {
      label: t("admin.navigation.settings"),
      icon: SettingsIcon,
      url: "/app/settings",
      selected: location.pathname.startsWith("/app/settings"),
      subNavigationItems: [
        {
          label: t("admin.navigation.generalSettings"),
          url: "/app/settings",
          selected: location.pathname === "/app/settings",
        },
        {
          label: t("admin.navigation.customizeWidget"),
          url: "/app/settings/widget",
          selected: location.pathname === "/app/settings/widget",
        },
        {
          label: t("admin.navigation.exchangeableProducts"),
          url: "/app/settings/products",
          selected: location.pathname === "/app/settings/products",
        },
      ],
    },
  ];

  const searchField = <SearchField />;

  return (
    <Frame
      navigation={
        <Navigation location={location.pathname}>
          <Navigation.Section
            items={navigationItems}
          />
          <Navigation.Section
            items={secondaryNavItems}
            separator
          />
        </Navigation>
      }
      topBar={
        <TopBar
          showNavigationToggle
         
          searchField={searchField}
          userMenu={
            <InlineStack gap="200" align="center">
              <LanguageSelector />
            </InlineStack>
          }
        />
      }
    >
      {/* narrowWidth || fullWidth */}
      <Page title={title} >
        {children}
      </Page>
    </Frame>
  );
}
