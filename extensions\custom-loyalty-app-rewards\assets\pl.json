{"widget": {"title": "Program Lojalnościowy", "pointsLabel": "pkt", "loading": "Ładowanie twoich punktów...", "guest": {"title": "Dołącz do naszego programu!", "description": "Zdobywaj punkty przy każdym zakupie i odblokowuj ekskluzywne nagrody.", "joinButton": "Dołącz do programu", "loginButton": "<PERSON><PERSON><PERSON><PERSON>"}, "member": {"defaultName": "Klient", "statusGuest": "<PERSON><PERSON><PERSON>", "statusMember": "Członek", "pointsLabel": "<PERSON><PERSON>", "ordersLabel": "Zamówienia", "nextReward": "Następna nagroda", "pointsUnit": "punkty", "progressMessage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, aby odblokować następną nagrodę!", "rewardAvailable": "Nagroda dostępna!"}, "navigation": {"yourRewards": "<PERSON><PERSON> na<PERSON>", "waysToEarn": "Sposoby zdobywania", "waysToRedeem": "<PERSON><PERSON><PERSON><PERSON> wymiany", "yourActivity": "<PERSON><PERSON>", "referFriends": "<PERSON><PERSON>", "redeemDescription": "Wymień swoje punkty na nagrody"}, "rewards": {"title": "<PERSON><PERSON> na<PERSON>", "pastRewards": "Poprzednie nagrody", "available": "Dostępne", "insufficientPoints": "Niewystarczające punkty", "redeemNow": "Wymień teraz", "needPoints": "Potrzebujesz {points} punktów", "fromPoints": "Od {points} punktów", "noRewards": "<PERSON>rak opcji wymiany.", "noRewardsEarn": "Brak dostępnych nagród. Zdobądź więcej punktów, aby odblokować nagrody!", "noPastRewards": "Brak poprzednich nagród.", "loadError": "Nie można załadować nagród. Spróbuj ponownie później.", "countSingle": "<PERSON><PERSON> {count} dostępną nagrodę", "countMultiple": "<PERSON><PERSON> {count} dostępnych nagród", "countZero": "Brak dostępnych nagród"}, "earn": {"title": "Sposoby zdobywania", "noWays": "Brak skonfigurowanych sposobów zdobywania punktów.", "perEuro": "{points} punktów za każde wydane €1", "perOrder": "{points} punktów za zamówienie", "forJoining": "{points} punktów za dołączenie do programu", "forReview": "{points} punktów za napisanie recenzji", "forReferral": "{points} punktów za polecenie"}, "redeem": {"title": "<PERSON><PERSON><PERSON><PERSON> wymiany", "noOptions": "<PERSON>rak opcji wymiany."}, "activity": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pointsTab": "<PERSON><PERSON>", "referralsTab": "Polecenia", "notice": "Twoje saldo punktów może nie odzwierciedlać najnowszej aktywności", "noHistory": "Brak dostępnej historii.", "needPoints": "Potr<PERSON><PERSON><PERSON><PERSON> {points} więcej punktów"}, "referral": {"title": "<PERSON><PERSON>", "description": "Udostępnij ten link znajomym, aby zdobywać nagrody, gdy dołączą!", "completed": "{count} ukończonych poleceń", "facebook": "Facebook", "twitter": "X", "email": "E-mail", "shareMessage": "Udostępnij ten link! Zarabiasz {referrerPoints} punktów, a Twój znajomy otrzymuje {referredPoints} punktów po dołączeniu.", "status": {"completed": "Zakończone", "pending": "Oczek<PERSON><PERSON><PERSON><PERSON>"}, "linkShared": "Link polecający udostępniony", "pendingValidation": "Oczekuje na walidację"}, "coupon": {"pointsToExchange": "Liczba punktów do wymiany:", "couponValue": "<PERSON><PERSON><PERSON><PERSON> kuponu:", "redeem": "Wymień", "instructions": "Użyj tego kodu rabatowego przy następnym zamówieniu!", "applyCode": "Zastos<PERSON>j kod", "copy": "<PERSON><PERSON><PERSON><PERSON>", "confirmTitle": "Potwierdź wymianę", "confirmMessage": "<PERSON><PERSON><PERSON><PERSON><PERSON> <strong>{points} punktów</strong> na <strong>{reward}</strong>.", "confirmQuestion": "<PERSON>zy na pewno chcesz kontynuować?", "processing": "Przetwarzanie twojej wym<PERSON>y...", "successTitle": "<PERSON><PERSON>iana udana!", "successMessage": "Twój kod rabatowy został wygenerowany:", "successInstructions": "Użyj tego kodu przy następnym zamówieniu, aby skorzystać z nagrody.", "close": "Zamknij", "useNow": "Użyj teraz", "insufficientPoints": "Niewystarczające punkty. Potrzebujesz {points} więcej punktów na tę nagrodę.", "availablePoints": "<PERSON><PERSON> {points} dostępnych punktów.", "copied": "✅ Skopiowane!"}, "actions": {"cancel": "<PERSON><PERSON><PERSON>", "continue": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "back": "Wstecz", "retry": "Spróbuj ponownie"}, "error": {"title": "Ups! Wystąpił błąd", "message": "Nie można załadować informacji o lojalności.", "signupError": "Nie można zapisać cię do programu. Spróbuj ponownie.", "redeemError": "Wystąpił błąd podczas wymiany. Spróbuj ponownie.", "communicationError": "Błąd komunikacji", "loadCustomer": "Błąd ładowania danych klienta", "loadEarnWays": "Błąd ładowania sposobów zdobywania", "loadRedeemWays": "Błąd ładowania sposobów wymiany", "loadProgramInfo": "Błąd ładowania informacji o programie", "loadProducts": "Błąd ładowania produktów do wymiany", "loadReferral": "Błąd ładowania linku polecającego", "loadRewards": "Błąd ładowania nagród", "loadActivity": "Błąd ładowania aktywności", "loadReferrals": "Błąd ładowania poleceń", "fetchRewards": "<PERSON>e udało się pobrać nagród", "fetchActivity": "<PERSON>e udało się pobrać aktywności", "fetchReferrals": "<PERSON><PERSON> udało się pobrać poleceń", "rewardNotFound": "Nagroda nie znaleziona lub niedostępna", "rewardDetails": "Nie można wyświetlić szczegółów nagrody. Spróbuj ponownie później.", "panelNotFound": "Panel nie znaleziony", "freeShipping": "Dar<PERSON><PERSON>"}, "schema": {"name": "Niestandardowa Aplikacja Nagród", "configHeader": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> widgetu", "positionLabel": "<PERSON><PERSON><PERSON><PERSON> widge<PERSON>", "positionBottomRight": "<PERSON><PERSON><PERSON> p<PERSON>", "positionBottomLeft": "<PERSON><PERSON><PERSON>", "positionTopRight": "Góra prawa", "positionTopLeft": "<PERSON><PERSON><PERSON> lewa", "primaryColorLabel": "<PERSON><PERSON> głów<PERSON>", "secondaryColorLabel": "<PERSON><PERSON>", "showOnMobileLabel": "Pokaż na urządzeniach mobilnych", "autoOpenLabel": "Automatyczne otwieranie dla nowych odwiedzających"}, "messages": {"pointsRemaining": "Pozostałe punkty", "signupWithReferral": "Rejestracja z kodem polecającym", "referralUsed": "Kod polecający użyty i usunięty", "customerIdRequired": "ID klienta wymagane do rejestracji"}}}