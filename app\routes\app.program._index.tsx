import { json, type LoaderFunctionArgs, type ActionFunctionArgs } from "@remix-run/node";
import { useLoaderData, useSubmit } from "@remix-run/react";
import { authenticate } from "../shopify.server";
import { getProgramStats } from "../models/ProgramStats.server";
import {
  Page,
  Layout,
  Card,
  Banner,
  Button,
  Text,
  ButtonGroup,
  TextField,
  Box,
  Toast,
  Frame,
  BlockStack,
} from "@shopify/polaris";
import { AdminLayout } from "../components/Layout/AdminLayout";
import { getProgramSettings } from "../models/ProgramSettings.server";
import { useState, useCallback } from "react";
import { useTranslation } from "../hooks/useTranslation";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const { session } = await authenticate.admin(request);
  const shop = session.shop;

  try {
    // Récupérer les paramètres du programme
    const settings = await getProgramSettings();

    // Récupérer les statistiques du programme
    const stats = await getProgramStats(shop);

    return json({
      settings: settings || { status: false, name: "", description: "" },
      stats
    });
  } catch (error) {
    console.error("Error loading program data:", error);
    return json({
      settings: { status: false, name: "", description: "" },
      stats: {
        totalCustomers: 0,
        activeCustomers: 0,
        totalPointsEarned: 0,
        totalPointsRedeemed: 0,
        totalRewards: 0,
        pendingReferrals: 0,
        completedReferrals: 0,
        pointsDistributed: 0
      }
    });
  }
};

export default function ProgramIndex() {
  const loaderData = useLoaderData<typeof loader>();
  const initialSettings = loaderData.settings;
  const stats = (loaderData as any).stats;
  const submit = useSubmit();
  const { t } = useTranslation();

  // État local pour les paramètres
  const [settings, setSettings] = useState(initialSettings);
  const [hasChanges, setHasChanges] = useState(false);
  const [toastActive, setToastActive] = useState(false);
  const [toastMessage, setToastMessage] = useState("");

  const showToast = (message: string) => {
    setToastMessage(message);
    setToastActive(true);
  };

  const handleToggleStatus = () => {
    const formData = new FormData();
    formData.append("action", "toggle");
    submit(formData, {
      method: "post",
      action: "/api/program/settings",
      preventScrollReset: true,
    });
    showToast(settings?.status ? t("admin.program.status.deactivate") : t("admin.program.status.activate"));
  };

  // Handler pour mettre à jour l'état local
  const handleUpdateSettings = useCallback((field: string, value: string) => {
    setSettings(prev => ({
      ...prev,
      [field]: value
    }));
    setHasChanges(true);
  }, []);

  // Handler pour sauvegarder les modifications
  const handleSaveSettings = () => {
    const formData = new FormData();
    formData.append("action", "update");
    formData.append("name", settings?.name || "");
    formData.append("description", settings?.description || "");

    submit(formData, {
      method: "post",
      action: "/api/program/settings",
      preventScrollReset: true,
    });

    setHasChanges(false);
    showToast(t("admin.program.paramSaveSuccess"));
  };

  const toastMarkup = toastActive ? (
    <Toast
      content={toastMessage}
      onDismiss={() => setToastActive(false)}
      duration={4000}
    />
  ) : null;

  return (
    <Frame>
      <AdminLayout title={t("admin.program.title")}>
        <Page fullWidth>
          <Layout>
            <Layout.Section>
              <Banner
                tone={settings?.status ? "success" : "warning"}
                action={{
                  content: settings?.status ? t("admin.program.status.deactivate") : t("admin.program.status.activate"),
                  onAction: handleToggleStatus,
                }}
              >
                <p>
                  <Text variant="headingMd" as="h2">
                    {settings?.status ? t("admin.program.status.active") : t("admin.program.status.inactive")}
                  </Text>
                  <br />
                  {settings?.status
                    ? t("admin.program.status.activeDescription")
                    : t("admin.program.status.inactiveDescription")}
                </p>
              </Banner>
            </Layout.Section>

            <Layout.Section>
              <Card>
                <Box padding="400">
                  <Text variant="headingMd" as="h2">{t("admin.program.generalConfiguration")}</Text>
                  <Box padding="400">
                    <TextField
                      label={t("admin.program.programName")}
                      value={settings?.name || ""}
                      onChange={(value) => handleUpdateSettings("name", value)}
                      autoComplete="off"
                    />
                    <Box padding="400">
                      <TextField
                        label={t("admin.program.programDescription")}
                        value={settings?.description || ""}
                        onChange={(value) => handleUpdateSettings("description", value)}
                        multiline={4}
                        autoComplete="off"
                      />
                    </Box>

                    {/* Bouton de sauvegarde */}
                    {hasChanges && (
                      <Box padding="400">
                        <Button
                          variant="primary"
                          onClick={handleSaveSettings}
                          size="large"
                        >
                          💾 {t("admin.program.saveModifications")}
                        </Button>
                        <Box paddingBlockStart="200">
                          <Text as="p" variant="bodySm" tone="subdued">
                            {t("admin.program.saveDescription")}
                          </Text>
                        </Box>
                      </Box>
                    )}
                  </Box>
                </Box>
              </Card>
            </Layout.Section>

            <Layout.Section variant="oneThird">
              <Card>
                <Box padding="400">
                  <Text variant="headingMd" as="h2">📊 {t("admin.program.stats.title")}</Text>
                  <Box padding="400">
                    <BlockStack gap="300">
                      <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px' }}>
                        <div style={{ textAlign: 'center', padding: '16px', background: '#f6f6f7', borderRadius: '8px' }}>
                          <Text variant="headingLg" as="h3">{stats?.totalCustomers || 0}</Text>
                          <Text variant="bodySm" as="p" tone="subdued">{t("admin.program.stats.totalCustomers")}</Text>
                        </div>
                        <div style={{ textAlign: 'center', padding: '16px', background: '#f6f6f7', borderRadius: '8px' }}>
                          <Text variant="headingLg" as="h3">{stats?.activeCustomers || 0}</Text>
                          <Text variant="bodySm" as="p" tone="subdued">{t("admin.program.stats.activeCustomers")}</Text>
                        </div>
                        <div style={{ textAlign: 'center', padding: '16px', background: '#f6f6f7', borderRadius: '8px' }}>
                          <Text variant="headingLg" as="h3">{stats?.totalPointsEarned || 0}</Text>
                          <Text variant="bodySm" as="p" tone="subdued">{t("admin.program.stats.totalPointsEarned")}</Text>
                        </div>
                        <div style={{ textAlign: 'center', padding: '16px', background: '#f6f6f7', borderRadius: '8px' }}>
                          <Text variant="headingLg" as="h3">{stats?.totalRewards || 0}</Text>
                          <Text variant="bodySm" as="p" tone="subdued">{t("admin.program.stats.totalRewards")}</Text>
                        </div>
                      </div>
                    </BlockStack>
                  </Box>
                </Box>
              </Card>

              <Card>
                <Box padding="400">
                  <Text variant="headingMd" as="h2">🚀 {t("admin.program.quickActions")}</Text>
                  <Box padding="400">
                    <ButtonGroup fullWidth>
                      <Button url="/app/program/points">{t("admin.program.pointsConfiguration")}</Button>
                      <Button url="/app/program/referrals">{t("admin.program.referralProgram")}</Button>
                    </ButtonGroup>
                  </Box>
                </Box>
              </Card>
            </Layout.Section>
          </Layout>
          {toastMarkup}
        </Page>
      </AdminLayout>
    </Frame>
  );
}
