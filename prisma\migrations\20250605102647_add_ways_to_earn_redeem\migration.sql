-- CreateTable
CREATE TABLE "WayToEarn" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "shop" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "earningType" TEXT NOT NULL DEFAULT 'increments',
    "earningValue" REAL NOT NULL,
    "icon" TEXT NOT NULL DEFAULT 'order',
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    CONSTRAINT "WayToEarn_shop_fkey" FOREIGN KEY ("shop") REFERENCES "Settings" ("shop") ON DELETE RESTRICT ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "WayToRedeem" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "shop" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "redeemType" TEXT NOT NULL DEFAULT 'discount',
    "redeemValue" REAL NOT NULL,
    "pointsCost" INTEGER NOT NULL,
    "icon" TEXT NOT NULL DEFAULT 'discount',
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    CONSTRAINT "WayToRedeem_shop_fkey" FOREIGN KEY ("shop") REFERENCES "Settings" ("shop") ON DELETE RESTRICT ON UPDATE CASCADE
);

-- CreateIndex
CREATE INDEX "WayToEarn_shop_idx" ON "WayToEarn"("shop");

-- CreateIndex
CREATE INDEX "WayToEarn_isActive_idx" ON "WayToEarn"("isActive");

-- CreateIndex
CREATE INDEX "WayToRedeem_shop_idx" ON "WayToRedeem"("shop");

-- CreateIndex
CREATE INDEX "WayToRedeem_isActive_idx" ON "WayToRedeem"("isActive");
