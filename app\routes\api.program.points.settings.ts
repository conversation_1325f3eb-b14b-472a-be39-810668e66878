import { json, redirect } from "@remix-run/node";
import type { ActionFunction, LoaderFunction } from "@remix-run/node";
import { authenticate } from "app/shopify.server";
import { getPointsSettings, updatePointsSettings } from "../models/PointsSettings.server";

export const loader: LoaderFunction = async ({ request }) => {
  const { session } = await authenticate.admin(request);
  const settings = await getPointsSettings(session.shop);
  return json(settings || {
    earningRate: 0,
    redemptionRate: 0,
    minimumPoints: 0,
    expirationDays: 0,
    referralPoints: 0,
    birthdayPoints: 0
  });
};

export const action: ActionFunction = async ({ request }) => {
  const { session } = await authenticate.admin(request);
  const formData = await request.formData();
  const action = formData.get("action");

  try {
    switch (action) {
      case "update": {
        const field = Array.from(formData.keys())[1]; // Le premier est "action"
        const value = formData.get(field);

        if (!field || !value) {
          return json({ error: "Champ ou valeur manquant" }, { status: 400 });
        }

        await updatePointsSettings(session.shop, {
          [field]: Number(value)
        });

        return json({ success: true });
      }

      case "updateAll": {
        const data = {
          earningRate: Number(formData.get("earningRate")),
          redemptionRate: Number(formData.get("redemptionRate")),
          minimumPoints: Number(formData.get("minimumPoints")),
          expirationDays: Number(formData.get("expirationDays")),
          referralPoints: Number(formData.get("referralPoints")),
          birthdayPoints: Number(formData.get("birthdayPoints")),
        };

        // Validation des données
        if (data.earningRate <= 0 || data.redemptionRate <= 0 || data.minimumPoints < 0 || data.expirationDays < 0) {
          return json({ error: "Les valeurs doivent être positives" }, { status: 400 });
        }

        await updatePointsSettings(session.shop, data);
        return json({ success: true });
      }

      default:
        return json({ error: "Action non reconnue" }, { status: 400 });
    }
  } catch (error) {
    console.error("Erreur lors de la mise à jour des paramètres :", error);
    return json({ error: "Erreur lors de la mise à jour des paramètres" }, { status: 500 });
  }
};
