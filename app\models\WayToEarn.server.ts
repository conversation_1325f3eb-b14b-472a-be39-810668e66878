import prisma from "../db.server";

export interface WayToEarn {
  id: string;
  shop: string;
  name: string;
  description: string;
  actionType: string;
  earningType: "increments" | "fixed";
  earningValue: number;
  icon: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateWayToEarnData {
  name: string;
  description: string;
  actionType: string;
  earningType: "increments" | "fixed";
  earningValue: number;
  icon?: string;
  isActive?: boolean;
}

export interface UpdateWayToEarnData extends Partial<CreateWayToEarnData> {}

export async function getWaysToEarn(shop: string) {
  try {
    if (!prisma) {
      console.error("Prisma client not initialized");
      return [];
    }

    return await prisma.wayToEarn.findMany({
      where: { shop },
      orderBy: { createdAt: "desc" }
    });
  } catch (error) {
    console.error("Error fetching ways to earn:", error);
    return [];
  }
}

export async function getWayToEarnById(id: string, shop: string) {
  try {
    if (!prisma) {
      console.error("Prisma client not initialized");
      return null;
    }

    return await prisma.wayToEarn.findFirst({
      where: { id, shop }
    });
  } catch (error) {
    console.error("Error fetching way to earn:", error);
    return null;
  }
}

export async function createWayToEarn(shop: string, data: CreateWayToEarnData) {
  try {
    if (!prisma) {
      console.error("Prisma client not initialized");
      return null;
    }

    return await prisma.wayToEarn.create({
      data: {
        shop,
        name: data.name,
        description: data.description,
        actionType: data.actionType,
        earningType: data.earningType,
        earningValue: data.earningValue,
        icon: data.icon || "order",
        isActive: data.isActive ?? true
      }
    });
  } catch (error) {
    console.error("Error creating way to earn:", error);
    return null;
  }
}

export async function updateWayToEarn(id: string, shop: string, data: UpdateWayToEarnData) {
  try {
    if (!prisma) {
      console.error("Prisma client not initialized");
      return null;
    }

    return await prisma.wayToEarn.update({
      where: { id },
      data: {
        ...data,
        updatedAt: new Date()
      }
    });
  } catch (error) {
    console.error("Error updating way to earn:", error);
    return null;
  }
}

export async function deleteWayToEarn(id: string, shop: string) {
  try {
    if (!prisma) {
      console.error("Prisma client not initialized");
      return false;
    }

    await prisma.wayToEarn.delete({
      where: { id }
    });
    return true;
  } catch (error) {
    console.error("Error deleting way to earn:", error);
    return false;
  }
}

export async function initializeDefaultWayToEarn(shop: string) {
  try {
    const existingWays = await getWaysToEarn(shop);
    if (existingWays.length > 0) {
      return existingWays;
    }

    // Créer les ways to earn par défaut
    const defaultWays = [
      {
        name: "Passer une commande",
        description: "Gagnez des points à chaque achat",
        actionType: "order",
        earningType: "increments" as const,
        earningValue: 5, // 5 points par €1 dépensé
        icon: "order",
        isActive: true
      },
      {
        name: "Inscription au programme",
        description: "Points de bienvenue pour les nouveaux membres",
        actionType: "signup",
        earningType: "fixed" as const,
        earningValue: 100, // 100 points fixes
        icon: "signup",
        isActive: true
      }
    ];

    const createdWays = [];
    for (const wayData of defaultWays) {
      const way = await createWayToEarn(shop, wayData);
      if (way) createdWays.push(way);
    }

    return createdWays;
  } catch (error) {
    console.error("Error initializing default way to earn:", error);
    return [];
  }
}
