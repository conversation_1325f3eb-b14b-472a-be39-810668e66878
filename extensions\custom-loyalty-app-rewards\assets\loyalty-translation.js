/**
 * Système de traduction pour le widget de fidélité côté client
 * Détecte automatiquement la langue via localization.country.iso_code
 * et charge les traductions appropriées
 */

(function() {
  'use strict';

  // Langues supportées
  const SUPPORTED_LANGUAGES = ['fr', 'en', 'de', 'es', 'it', 'nl', 'pt', 'pl'];

  // Mapping des codes pays vers les langues
  const COUNTRY_TO_LANGUAGE = {
    // Français
    'FR': 'fr',
    'BE': 'fr', // Belgique (par défaut français, peut être néerlandais selon la région)
    'CH': 'fr', // Suisse (par défaut français)
    'CA': 'fr', // Canada (peut être anglais selon la région)
    'MC': 'fr', // Monaco
    'LU': 'fr', // Luxembourg

    // Anglais
    'GB': 'en',
    'UK': 'en',
    'US': 'en',
    'AU': 'en', // Australie
    'NZ': 'en', // Nouve<PERSON>-<PERSON><PERSON><PERSON><PERSON>
    'IE': 'en', // Irlande
    'ZA': 'en', // Afrique du Sud
    'SG': 'en', // Singapour
    'HK': 'en', // Hong Kong
    'IN': 'en', // Inde

    // Allemand
    'DE': 'de',
    'AT': 'de', // Autriche
    'LI': 'de', // Liechtenstein

    // Espagnol
    'ES': 'es',
    'MX': 'es', // Mexique
    'AR': 'es', // Argentine
    'CO': 'es', // Colombie
    'PE': 'es', // Pérou
    'CL': 'es', // Chili
    'VE': 'es', // Venezuela
    'EC': 'es', // Équateur
    'GT': 'es', // Guatemala
    'CU': 'es', // Cuba
    'BO': 'es', // Bolivie
    'DO': 'es', // République dominicaine
    'HN': 'es', // Honduras
    'PY': 'es', // Paraguay
    'SV': 'es', // Salvador
    'NI': 'es', // Nicaragua
    'CR': 'es', // Costa Rica
    'PA': 'es', // Panama
    'UY': 'es', // Uruguay

    // Italien
    'IT': 'it',
    'SM': 'it', // Saint-Marin
    'VA': 'it', // Vatican

    // Néerlandais
    'NL': 'nl',
    'SR': 'nl', // Suriname

    // Portugais
    'PT': 'pt',
    'BR': 'pt', // Brésil
    'AO': 'pt', // Angola
    'MZ': 'pt', // Mozambique
    'CV': 'pt', // Cap-Vert
    'GW': 'pt', // Guinée-Bissau
    'ST': 'pt', // São Tomé-et-Príncipe
    'TL': 'pt', // Timor oriental

    // Polonais
    'PL': 'pl'
  };

  // Cache pour les traductions chargées
  let translationsCache = {};
  let currentLanguage = 'en'; // Langue par défaut

  /**
   * Détecte la langue du site via Shopify Liquid
   * Utilise localization.country.iso_code ou d'autres méthodes de détection
   */
  function detectLanguage() {
    try {
      // Méthode 1: Via la configuration Liquid (si disponible)
      if (window.loyaltyWidgetConfig && window.loyaltyWidgetConfig.countryCode) {
        const countryCode = window.loyaltyWidgetConfig.countryCode.toUpperCase();
        if (COUNTRY_TO_LANGUAGE[countryCode]) {
          return COUNTRY_TO_LANGUAGE[countryCode];
        }
      }

      // Méthode 2: Via Shopify.country (si disponible)
      if (typeof Shopify !== 'undefined' && Shopify.country) {
        const countryCode = Shopify.country.toUpperCase();
        if (COUNTRY_TO_LANGUAGE[countryCode]) {
          return COUNTRY_TO_LANGUAGE[countryCode];
        }
      }

      // Méthode 3: Via la locale du navigateur
      const browserLang = navigator.language || navigator.userLanguage;
      if (browserLang) {
        const langCode = browserLang.split('-')[0].toLowerCase();
        if (SUPPORTED_LANGUAGES.includes(langCode)) {
          return langCode;
        }
      }

      // Méthode 4: Via l'URL ou le domaine (pour les sites multilingues)
      const hostname = window.location.hostname;
      const pathname = window.location.pathname;
      
      // Vérifier les sous-domaines (ex: fr.example.com)
      const subdomain = hostname.split('.')[0];
      if (SUPPORTED_LANGUAGES.includes(subdomain)) {
        return subdomain;
      }

      // Vérifier les chemins (ex: /fr/, /en/)
      const pathLang = pathname.split('/')[1];
      if (SUPPORTED_LANGUAGES.includes(pathLang)) {
        return pathLang;
      }

      // Fallback: anglais par défaut
      return 'en';
    } catch (error) {
      console.warn('Erreur lors de la détection de langue:', error);
      return 'en';
    }
  }

  /**
   * Charge les traductions pour une langue donnée
   */
  async function loadTranslations(language) {
    if (translationsCache[language]) {
      return translationsCache[language];
    }

    try {
      // Utiliser l'URL directe depuis la configuration Liquid
      const translationUrl = window.loyaltyTranslationUrls && window.loyaltyTranslationUrls[language]
        ? window.loyaltyTranslationUrls[language]
        : `/assets/${language}.json`;

      const response = await fetch(translationUrl);
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const translations = await response.json();
      translationsCache[language] = translations;
      return translations;
    } catch (error) {
      console.warn(`Impossible de charger les traductions pour ${language}:`, error);

      // Fallback vers l'anglais si ce n'est pas déjà l'anglais
      if (language !== 'en') {
        return loadTranslations('en');
      }

      // Si même l'anglais échoue, retourner un objet vide
      return { widget: {} };
    }
  }

  /**
   * Récupère une traduction à partir d'une clé imbriquée
   * Exemple: t('widget.member.pointsLabel') va chercher translations.widget.member.pointsLabel
   */
  function getTranslation(translations, keyParts) {
    let current = translations;
    for (const part of keyParts) {
      if (current && typeof current === 'object' && current[part] !== undefined) {
        current = current[part];
      } else {
        return null;
      }
    }
    return typeof current === 'string' ? current : null;
  }

  /**
   * Fonction principale de traduction
   */
  function translate(key, params = {}) {
    if (!key) {
      console.warn('Clé de traduction vide');
      return '';
    }

    const keyParts = key.split('.');
    const translations = translationsCache[currentLanguage];

    if (!translations) {
      console.warn(`Traductions non chargées pour ${currentLanguage}`);
      return key;
    }

    // Essayer de trouver la traduction dans la langue actuelle
    let translation = getTranslation(translations, keyParts);

    // Si la traduction n'existe pas dans la langue actuelle, essayer avec l'anglais comme fallback
    if (translation === null && currentLanguage !== 'en') {
      const englishTranslations = translationsCache['en'];
      if (englishTranslations) {
        translation = getTranslation(englishTranslations, keyParts);
      }

      // Si toujours pas de traduction, essayer avec le français comme second fallback
      if (translation === null) {
        const frenchTranslations = translationsCache['fr'];
        if (frenchTranslations) {
          translation = getTranslation(frenchTranslations, keyParts);
        }
      }
    }

    // Si aucune traduction n'est trouvée, utiliser la clé comme fallback
    if (translation === null) {
      console.warn(`Traduction manquante pour la clé: ${key}`);
      return key;
    }

    // Remplacer les paramètres dans la traduction
    let result = translation;
    for (const [paramKey, paramValue] of Object.entries(params)) {
      const regex = new RegExp(`{${paramKey}}`, 'g');
      result = result.replace(regex, String(paramValue));
    }

    return result;
  }

  /**
   * Initialise le système de traduction
   */
  async function initTranslation() {
    // Détecter la langue
    currentLanguage = detectLanguage();
    console.log(`Langue détectée: ${currentLanguage}`);

    // Charger les traductions pour la langue détectée
    await loadTranslations(currentLanguage);

    // Charger aussi l'anglais comme fallback
    if (currentLanguage !== 'en') {
      await loadTranslations('en');
    }

    // Charger aussi le français comme second fallback
    if (currentLanguage !== 'fr') {
      await loadTranslations('fr');
    }

    console.log('Système de traduction initialisé');

    // Émettre un événement pour signaler que les traductions sont prêtes
    window.dispatchEvent(new CustomEvent('loyaltyTranslationsReady'));
  }

  /**
   * Obtient la langue actuelle
   */
  function getCurrentLanguage() {
    return currentLanguage;
  }

  /**
   * Vérifie si une clé de traduction existe
   */
  function hasTranslation(key) {
    if (!key) return false;

    const keyParts = key.split('.');
    const translations = translationsCache[currentLanguage];

    if (!translations) return false;

    return getTranslation(translations, keyParts) !== null;
  }

  // Exposer les fonctions globalement
  window.LoyaltyTranslation = {
    init: initTranslation,
    t: translate,
    translate: translate,
    getCurrentLanguage: getCurrentLanguage,
    hasTranslation: hasTranslation,
    detectLanguage: detectLanguage
  };

  // Auto-initialisation si le DOM est prêt
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initTranslation);
  } else {
    initTranslation();
  }

})();
