/*
  Warnings:

  - Added the required column `shopifyCustomerId` to the `Reward` table without a default value. This is not possible if the table is not empty.
  - Added the required column `updatedAt` to the `Reward` table without a default value. This is not possible if the table is not empty.

*/
-- RedefineTables
PRAGMA defer_foreign_keys=ON;
PRAGMA foreign_keys=OFF;
CREATE TABLE "new_Reward" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "customerId" TEXT NOT NULL,
    "shopifyCustomerId" TEXT NOT NULL,
    "shop" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "value" REAL NOT NULL,
    "pointsCost" INTEGER NOT NULL,
    "code" TEXT,
    "discountId" TEXT,
    "status" TEXT NOT NULL DEFAULT 'active',
    "expiresAt" DATETIME,
    "usedAt" DATETIME,
    "orderId" TEXT,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    CONSTRAINT "Reward_customerId_fkey" FOREIGN KEY ("customerId") REFERENCES "customers" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
);
INSERT INTO "new_Reward" ("code", "createdAt", "customerId", "expiresAt", "id", "name", "pointsCost", "shop", "status", "type", "usedAt", "value") SELECT "code", "createdAt", "customerId", "expiresAt", "id", "name", "pointsCost", "shop", "status", "type", "usedAt", "value" FROM "Reward";
DROP TABLE "Reward";
ALTER TABLE "new_Reward" RENAME TO "Reward";
CREATE INDEX "Reward_shop_idx" ON "Reward"("shop");
CREATE INDEX "Reward_customerId_idx" ON "Reward"("customerId");
CREATE INDEX "Reward_shopifyCustomerId_idx" ON "Reward"("shopifyCustomerId");
CREATE INDEX "Reward_status_idx" ON "Reward"("status");
CREATE INDEX "Reward_code_idx" ON "Reward"("code");
CREATE INDEX "Reward_createdAt_idx" ON "Reward"("createdAt");
CREATE TABLE "new_Settings" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "shop" TEXT NOT NULL,
    "earningRate" REAL NOT NULL DEFAULT 1.0,
    "redemptionRate" REAL NOT NULL DEFAULT 100.0,
    "minimumPoints" INTEGER NOT NULL DEFAULT 100,
    "expirationDays" INTEGER NOT NULL DEFAULT 365,
    "referralPoints" INTEGER NOT NULL DEFAULT 100,
    "birthdayPoints" INTEGER NOT NULL DEFAULT 250,
    "widgetEnabled" BOOLEAN NOT NULL DEFAULT true,
    "primaryColor" TEXT NOT NULL DEFAULT '#000000',
    "language" TEXT NOT NULL DEFAULT 'fr',
    "exchangeableProducts" TEXT,
    "widgetSecondaryColor" TEXT NOT NULL DEFAULT '#4CAF50',
    "widgetTextColor" TEXT NOT NULL DEFAULT '#FFFFFF',
    "widgetPosition" TEXT NOT NULL DEFAULT 'bottom-right',
    "widgetSize" TEXT NOT NULL DEFAULT 'medium',
    "widgetBorderRadius" TEXT NOT NULL DEFAULT 'rounded',
    "widgetShadow" BOOLEAN NOT NULL DEFAULT true,
    "widgetAnimation" BOOLEAN NOT NULL DEFAULT true,
    "showPointsOnButton" BOOLEAN NOT NULL DEFAULT true,
    "pointsName" TEXT NOT NULL DEFAULT 'Points',
    "welcomeMessage" TEXT NOT NULL DEFAULT 'Bienvenue dans notre programme de fidélité !',
    "shopName" TEXT,
    "currency" TEXT NOT NULL DEFAULT 'EUR',
    "customCSS" TEXT,
    "emailNotifications" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL
);
INSERT INTO "new_Settings" ("birthdayPoints", "createdAt", "currency", "customCSS", "earningRate", "emailNotifications", "exchangeableProducts", "expirationDays", "id", "language", "minimumPoints", "pointsName", "primaryColor", "redemptionRate", "referralPoints", "shop", "shopName", "showPointsOnButton", "updatedAt", "welcomeMessage", "widgetAnimation", "widgetBorderRadius", "widgetEnabled", "widgetPosition", "widgetSecondaryColor", "widgetShadow", "widgetSize", "widgetTextColor") SELECT "birthdayPoints", "createdAt", "currency", "customCSS", "earningRate", "emailNotifications", "exchangeableProducts", "expirationDays", "id", "language", "minimumPoints", "pointsName", "primaryColor", "redemptionRate", "referralPoints", "shop", "shopName", "showPointsOnButton", "updatedAt", "welcomeMessage", "widgetAnimation", "widgetBorderRadius", "widgetEnabled", "widgetPosition", "widgetSecondaryColor", "widgetShadow", "widgetSize", "widgetTextColor" FROM "Settings";
DROP TABLE "Settings";
ALTER TABLE "new_Settings" RENAME TO "Settings";
CREATE UNIQUE INDEX "Settings_shop_key" ON "Settings"("shop");
CREATE TABLE "new_WayToRedeem" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "shop" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "redeemType" TEXT NOT NULL DEFAULT 'discount',
    "redeemValue" REAL NOT NULL,
    "pointsCost" INTEGER NOT NULL,
    "icon" TEXT NOT NULL DEFAULT 'discount',
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "isConfigurable" BOOLEAN NOT NULL DEFAULT false,
    "minPoints" INTEGER,
    "maxPoints" INTEGER,
    "minValue" REAL,
    "maxValue" REAL,
    "expiryDays" INTEGER NOT NULL DEFAULT 30,
    "usageLimit" INTEGER NOT NULL DEFAULT 1,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    CONSTRAINT "WayToRedeem_shop_fkey" FOREIGN KEY ("shop") REFERENCES "Settings" ("shop") ON DELETE RESTRICT ON UPDATE CASCADE
);
INSERT INTO "new_WayToRedeem" ("createdAt", "description", "icon", "id", "isActive", "name", "pointsCost", "redeemType", "redeemValue", "shop", "updatedAt") SELECT "createdAt", "description", "icon", "id", "isActive", "name", "pointsCost", "redeemType", "redeemValue", "shop", "updatedAt" FROM "WayToRedeem";
DROP TABLE "WayToRedeem";
ALTER TABLE "new_WayToRedeem" RENAME TO "WayToRedeem";
CREATE INDEX "WayToRedeem_shop_idx" ON "WayToRedeem"("shop");
CREATE INDEX "WayToRedeem_isActive_idx" ON "WayToRedeem"("isActive");
CREATE INDEX "WayToRedeem_redeemType_idx" ON "WayToRedeem"("redeemType");
PRAGMA foreign_keys=ON;
PRAGMA defer_foreign_keys=OFF;
