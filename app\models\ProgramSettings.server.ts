import prisma from "../db.server";

export interface ProgramSettings {
  id: string;
  status: boolean;
  name: string;
  description: string;
  created_at: Date;
  updated_at: Date;
}

export async function getProgramSettings() {
  try {
    if (!prisma) {
      console.error("Prisma client not initialized");
      return null;
    }
    
    return await prisma.programSettings.findFirst({
      orderBy: { created_at: "desc" }
    });
  } catch (error) {
    console.error("Error fetching program settings:", error);
    return null;
  }
}

export async function updateProgramSettings(data: Partial<ProgramSettings>) {
  try {
    if (!prisma) {
      console.error("Prisma client not initialized");
      return null;
    }

    const currentSettings = await getProgramSettings();
    
    if (currentSettings) {
      return await prisma.programSettings.update({
        where: { id: currentSettings.id },
        data: {
          ...data,
          updated_at: new Date()
        }
      });
    }

    return await prisma.programSettings.create({
      data: {
        status: data.status || false,
        name: data.name || "Programme de fidélité",
        description: data.description || "",
        created_at: new Date(),
        updated_at: new Date()
      }
    });
  } catch (error) {
    console.error("Error updating program settings:", error);
    return null;
  }
}

export async function toggleProgramStatus() {
  try {
    if (!prisma) {
      console.error("Prisma client not initialized");
      return null;
    }

    const currentSettings = await getProgramSettings();
    if (!currentSettings) {
      return await updateProgramSettings({ status: true });
    }

    return await prisma.programSettings.update({
      where: { id: currentSettings.id },
      data: {
        status: !currentSettings.status,
        updated_at: new Date()
      }
    });
  } catch (error) {
    console.error("Error toggling program status:", error);
    return null;
  }
} 