import prisma from "../db.server";

export interface PointsSettings {
  earningRate: number;
  redemptionRate: number;
  minimumPoints: number;
  expirationDays: number;
  referralPoints: number;
  birthdayPoints: number;
}

export async function getPointsSettings(shop: string) {
  try {
    if (!prisma) {
      console.error("Prisma client not initialized");
      return null;
    }

    return await prisma.settings.findUnique({
      where: { shop },
      select: {
        earningRate: true,
        redemptionRate: true,
        minimumPoints: true,
        expirationDays: true,
        referralPoints: true,
        birthdayPoints: true
      }
    });
  } catch (error) {
    console.error("Error fetching points settings:", error);
    return null;
  }
}

export async function updatePointsSettings(shop: string, data: Partial<PointsSettings>) {
  try {
    if (!prisma) {
      console.error("Prisma client not initialized");
      return null;
    }

    const currentSettings = await getPointsSettings(shop);

    // Fonction pour s'assurer qu'une valeur est un nombre valide
    const ensureValidNumber = (value: any, defaultValue: number = 0): number => {
      const num = Number(value);
      return isNaN(num) ? defaultValue : num;
    };

    // Préparer les données à mettre à jour en s'assurant que toutes les valeurs sont des nombres valides
    const updateData = {
      earningRate: ensureValidNumber(data.earningRate, currentSettings?.earningRate ?? 0),
      redemptionRate: ensureValidNumber(data.redemptionRate, currentSettings?.redemptionRate ?? 0),
      minimumPoints: ensureValidNumber(data.minimumPoints, currentSettings?.minimumPoints ?? 0),
      expirationDays: ensureValidNumber(data.expirationDays, currentSettings?.expirationDays ?? 0),
      referralPoints: ensureValidNumber(data.referralPoints, currentSettings?.referralPoints ?? 0),
      birthdayPoints: ensureValidNumber(data.birthdayPoints, currentSettings?.birthdayPoints ?? 0)
    };

    if (currentSettings) {
      return await prisma.settings.update({
        where: { shop },
        data: {
          ...updateData,
          settingsHistory: {
            create: {
              changes: JSON.stringify({
                old: currentSettings,
                new: updateData
              })
            }
          }
        },
        select: {
          earningRate: true,
          redemptionRate: true,
          minimumPoints: true,
          expirationDays: true,
          referralPoints: true,
          birthdayPoints: true
        }
      });
    }

    return await prisma.settings.create({
      data: {
        shop,
        ...updateData
      },
      select: {
        earningRate: true,
        redemptionRate: true,
        minimumPoints: true,
        expirationDays: true,
        referralPoints: true,
        birthdayPoints: true
      }
    });
  } catch (error) {
    console.error("Error updating points settings:", error);
    return null;
  }
}
