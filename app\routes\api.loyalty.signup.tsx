import type { ActionFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/node";
import { promoteToMember } from "app/models/Customer.server";
import { awardPointsForSignup } from "app/services/pointsService.server";

export const action = async ({ request }: ActionFunctionArgs) => {
  if (request.method !== "POST") {
    return json({ error: "Method not allowed" }, { status: 405 });
  }

  const formData = await request.formData();
  const shop = formData.get("shop") as string;
  const customerId = formData.get("customerId") as string;
  const firstName = formData.get("firstName") as string;
  const lastName = formData.get("lastName") as string;
  const email = formData.get("email") as string;

  // Vérifications de sécurité
  if (!shop || !customerId) {
    return json({ error: "Shop and customerId are required" }, { status: 400 });
  }

  try {
    // Promouvoir le client en membre
    const updatedCustomer = await promoteToMember(customerId, shop);
    
    if (!updatedCustomer) {
      return json({ error: "Customer not found" }, { status: 404 });
    }

    // Attribuer les points de bienvenue
    const signupResult = await awardPointsForSignup(shop, customerId);

    const response = {
      success: true,
      customer: {
        id: updatedCustomer.id,
        customerId: updatedCustomer.customerId,
        firstName: updatedCustomer.firstName,
        lastName: updatedCustomer.lastName,
        email: updatedCustomer.email,
        type: updatedCustomer.type,
        points: updatedCustomer.points,
        totalSpent: updatedCustomer.totalSpent,
        ordersCount: updatedCustomer.ordersCount
      },
      pointsAwarded: signupResult?.points || 0,
      message: `Bienvenue dans notre programme de fidélité ! Vous avez reçu ${signupResult?.points || 0} points de bienvenue.`
    };

    return json(response, {
      headers: {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "POST",
        "Access-Control-Allow-Headers": "Content-Type",
      },
    });
  } catch (error) {
    console.error("Error during loyalty signup:", error);
    return json({ error: "Internal server error" }, { status: 500 });
  }
};

// Gérer les requêtes OPTIONS pour CORS
export const options = () => {
  return new Response(null, {
    status: 200,
    headers: {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "POST, OPTIONS",
      "Access-Control-Allow-Headers": "Content-Type",
    },
  });
};
