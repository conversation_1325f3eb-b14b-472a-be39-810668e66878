# Learn more about configuring your app at https://shopify.dev/docs/apps/tools/cli/configuration

client_id = "2f8697fde2cd8d13d910d27423aa93e3"
name = "Custom Loyalty Rewards App"
handle = "custom-loyalty-rewards-app-dev"
application_url = "https://visitor-peripherals-fits-african.trycloudflare.com"
embedded = true

[build]
automatically_update_urls_on_dev = true
include_config_on_deploy = true

[webhooks]
api_version = "2025-04"

  [[webhooks.subscriptions]]
  topics = [ "app/scopes_update" ]
  uri = "/webhooks/app/scopes_update"

  [[webhooks.subscriptions]]
  topics = [ "app/uninstalled" ]
  uri = "/webhooks/app/uninstalled"

  [[webhooks.subscriptions]]
  topics = [ "orders/cancelled" ]
  uri = "/webhooks/points/order-cancelled"

  [[webhooks.subscriptions]]
  topics = [ "orders/create" ]
  uri = "/webhooks/points/order-created"

  [[webhooks.subscriptions]]
  topics = [ "orders/fulfilled" ]
  uri = "/webhooks/points/order-fulfilled"

[access_scopes]
# Learn more at https://shopify.dev/docs/apps/tools/cli/configuration#access_scopes
scopes = "read_content,read_customers,read_gift_cards,read_marketing_events,read_online_store_navigation,read_online_store_pages,read_orders,write_app_proxy,write_content,write_discounts,write_gift_cards,write_marketing_events,write_products"

[auth]
redirect_urls = [
  "https://visitor-peripherals-fits-african.trycloudflare.com/auth/callback",
  "https://visitor-peripherals-fits-african.trycloudflare.com/auth/shopify/callback",
  "https://visitor-peripherals-fits-african.trycloudflare.com/api/auth/callback"
]

[app_proxy]
url = "https://visitor-peripherals-fits-african.trycloudflare.com/api/proxy"
subpath = "proxy"
prefix = "apps"

[pos]
embedded = true

[[app_embed]]
name = "Custom Rewards App"
handle = "custom-rewards-app"
url = "https://apt-harbor-ball-specifically.trycloudflare.com/embed/custom-rewards-app"
