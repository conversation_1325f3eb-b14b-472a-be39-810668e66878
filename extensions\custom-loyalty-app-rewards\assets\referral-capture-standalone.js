/**
 * Script standalone de capture des codes de parrainage
 * À inclure dans le thème pour capturer les codes même sans le widget
 * 
 * Instructions d'installation :
 * 1. Ajouter ce script dans les assets du thème
 * 2. L'inclure dans theme.liquid avant la fermeture de </head>
 * 
 * Exemple d'inclusion :
 * <script>{{ 'referral-capture-standalone.js' | asset_url | script_tag }}</script>
 */

(function() {
  'use strict';

  // Éviter les doublons si le script est déjà chargé
  if (window.LoyaltyReferralCaptured) {
    return;
  }
  window.LoyaltyReferralCaptured = true;

  // Configuration
  const CONFIG = {
    STORAGE_KEY: 'loyalty_referral_code',
    TIMESTAMP_KEY: 'loyalty_referral_timestamp',
    EXPIRATION_TIME: 24 * 60 * 60 * 1000, // 24 heures
    PARAM_NAME: 'ref',
    SHOW_NOTIFICATION: true
  };

  // Fonction pour capturer le code de parrainage
  function captureReferralCode() {
    try {
      const urlParams = new URLSearchParams(window.location.search);
      const referralCode = urlParams.get(CONFIG.PARAM_NAME);
      
      if (referralCode && referralCode.trim()) {
        // Stocker le code avec timestamp
        localStorage.setItem(CONFIG.STORAGE_KEY, referralCode.trim());
        localStorage.setItem(CONFIG.TIMESTAMP_KEY, Date.now().toString());
        
        console.log(`[Loyalty] Code de parrainage capturé: ${referralCode}`);
        
        // Nettoyer l'URL
        cleanUrl();
        
        // Afficher notification si activée
        if (CONFIG.SHOW_NOTIFICATION) {
          showNotification();
        }
        
        // Déclencher un événement personnalisé
        window.dispatchEvent(new CustomEvent('loyaltyReferralCaptured', {
          detail: { code: referralCode }
        }));
        
        return true;
      }
    } catch (error) {
      console.error('[Loyalty] Erreur capture code parrainage:', error);
    }
    return false;
  }

  // Nettoyer l'URL sans recharger la page
  function cleanUrl() {
    try {
      const url = new URL(window.location);
      url.searchParams.delete(CONFIG.PARAM_NAME);
      
      const cleanUrl = url.pathname + (url.search || '') + url.hash;
      window.history.replaceState({}, document.title, cleanUrl);
    } catch (error) {
      console.error('[Loyalty] Erreur nettoyage URL:', error);
    }
  }

  // Afficher une notification discrète
  function showNotification() {
    const notification = document.createElement('div');
    notification.innerHTML = `
      <div style="
        position: fixed;
        top: 20px;
        right: 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 16px 20px;
        border-radius: 12px;
        box-shadow: 0 8px 32px rgba(0,0,0,0.2);
        z-index: 2147483647;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        font-size: 14px;
        font-weight: 500;
        opacity: 0;
        transform: translateX(100%);
        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        max-width: 320px;
        pointer-events: none;
        backdrop-filter: blur(10px);
      ">
        <div style="display: flex; align-items: center; gap: 12px;">
          <div style="font-size: 20px;">🎉</div>
          <div>
            <div style="font-weight: 600; margin-bottom: 4px;">Lien de parrainage détecté !</div>
            <div style="font-size: 12px; opacity: 0.9;">Inscrivez-vous pour bénéficier des avantages</div>
          </div>
        </div>
      </div>
    `;
    
    document.body.appendChild(notification);
    const notifElement = notification.firstElementChild;
    
    // Animation d'entrée
    setTimeout(() => {
      notifElement.style.opacity = '1';
      notifElement.style.transform = 'translateX(0)';
    }, 100);
    
    // Animation de sortie
    setTimeout(() => {
      notifElement.style.opacity = '0';
      notifElement.style.transform = 'translateX(100%)';
      setTimeout(() => {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification);
        }
      }, 400);
    }, 4000);
  }

  // Vérifier la validité du code stocké
  function isCodeValid() {
    const timestamp = localStorage.getItem(CONFIG.TIMESTAMP_KEY);
    if (!timestamp) return false;
    
    const now = Date.now();
    const captureTime = parseInt(timestamp);
    return (now - captureTime) < CONFIG.EXPIRATION_TIME;
  }

  // Nettoyer les codes expirés
  function cleanExpiredCode() {
    if (!isCodeValid()) {
      localStorage.removeItem(CONFIG.STORAGE_KEY);
      localStorage.removeItem(CONFIG.TIMESTAMP_KEY);
      return true;
    }
    return false;
  }

  // API publique
  window.LoyaltyReferral = {
    // Obtenir le code valide
    getReferralCode: function() {
      cleanExpiredCode();
      return localStorage.getItem(CONFIG.STORAGE_KEY);
    },
    
    // Vérifier la validité
    isValid: function() {
      return isCodeValid() && !!localStorage.getItem(CONFIG.STORAGE_KEY);
    },
    
    // Nettoyer manuellement
    clear: function() {
      localStorage.removeItem(CONFIG.STORAGE_KEY);
      localStorage.removeItem(CONFIG.TIMESTAMP_KEY);
      console.log('[Loyalty] Code de parrainage supprimé');
    },
    
    // Obtenir les infos de debug
    getDebugInfo: function() {
      return {
        code: localStorage.getItem(CONFIG.STORAGE_KEY),
        timestamp: localStorage.getItem(CONFIG.TIMESTAMP_KEY),
        isValid: isCodeValid(),
        timeLeft: isCodeValid() ? CONFIG.EXPIRATION_TIME - (Date.now() - parseInt(localStorage.getItem(CONFIG.TIMESTAMP_KEY) || '0')) : 0
      };
    }
  };

  // Initialisation
  function init() {
    // Nettoyer les codes expirés au démarrage
    cleanExpiredCode();
    
    // Capturer immédiatement
    captureReferralCode();
    
    // Écouter les changements d'URL (pour les SPA)
    let lastUrl = location.href;
    const observer = new MutationObserver(() => {
      const url = location.href;
      if (url !== lastUrl) {
        lastUrl = url;
        setTimeout(captureReferralCode, 100);
      }
    });
    
    observer.observe(document, { subtree: true, childList: true });
    
    // Écouter les événements popstate
    window.addEventListener('popstate', () => {
      setTimeout(captureReferralCode, 100);
    });
  }

  // Démarrer selon l'état du DOM
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', init);
  } else {
    init();
  }

  // Debug en mode développement
  if (window.location.hostname === 'localhost' || window.location.hostname.includes('ngrok')) {
    console.log('[Loyalty] Script de capture des codes de parrainage chargé');
    console.log('[Loyalty] API disponible via window.LoyaltyReferral');
  }

})();
