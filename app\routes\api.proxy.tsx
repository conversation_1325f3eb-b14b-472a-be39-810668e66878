import type { LoaderFunctionArgs, ActionFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/node";
import { getCustomerByShopifyId, getCustomerById, promoteToMember, createCustomer, updateCustomerPoints } from "app/models/Customer.server";
import { getWaysToEarn } from "app/models/WayToEarn.server";
import { getWaysToRedeem } from "app/models/WayToRedeem.server";
import { getExchangeableProducts } from "app/models/ExchangeableProducts.server";
import { awardPointsForSignup } from "app/services/pointsService.server";
import { getPointsSettings } from "app/models/PointsSettings.server";
import { getProgramSettings } from "app/models/ProgramSettings.server";
import { getSiteSettings } from "app/models/SiteSettings.server";
import { createLoyaltyCoupon, calculateCouponValue } from "app/services/loyaltyCoupons.server";
import { createReferralLink, getCustomerReferrals, processReferralSignup } from "app/models/Referral.server";
import { getReferralSettings } from "app/models/ReferralSettings.server";
import prisma from "app/db.server";

/**
 * Routeur API centralisé pour l'App Proxy Shopify
 * Toutes les communications entre le widget et le backend passent par ici
 */

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const url = new URL(request.url);
  const shop = url.searchParams.get("shop");
  const path = url.searchParams.get("path_prefix") || "";
  const prepath = url.searchParams.get("prepath") || "";
  console.log("prepath", prepath);
  // Extraire l'endpoint depuis le path
  // const pathParts = path.split("/").filter(Boolean);
  // const endpoint = pathParts[0] || "widget";
  //   console.log("ap")
  if (!shop) {
    return new Response(JSON.stringify({ error: "Shop parameter required" }), {
      status: 400,
      headers: { "Content-Type": "application/json" },
    });
    // return json({ error: "Shop parameter required" }, { status: 400 });
  }

  try {
    switch (prepath) {
      case "widget":
        return await handleWidgetRequest(request, shop);

      case "customer":
        return await handleCustomerRequest(request, shop);

      case "ways-to-earn":
        return await handleWaysToEarnRequest(request, shop);

      case "ways-to-redeem":
        return await handleWaysToRedeemRequest(request, shop);

      case "program-info":
        return await handleProgramInfoRequest(request, shop);

      case "client-products":
        return await handleClientProductsRequest(request, shop);

      case "coupon-value":
        return await handleCouponValueRequest(request, shop);

      case "customer-rewards":
        return await handleCustomerRewardsRequest(request, shop);

      case "customer-history":
        return await handleCustomerHistoryRequest(request, shop);

      case "customer-referrals":
        return await handleCustomerReferralsRequest(request, shop);

      case "generate-referral":
        return await handleGenerateReferralRequest(request, shop);

      default:
        return await handleWidgetRequest(request, shop);
    }
  } catch (error) {
    console.error("Error in API proxy:", error);
    return new Response("Internal server error", {
      status: 500,
      headers: { "Content-Type": "text/plain" },
    });
    // return json({ error: "Internal server error" }, { status: 500 });
  }
};

export const action = async ({ request }: ActionFunctionArgs) => {
  const url = new URL(request.url);
  const shop = url.searchParams.get("shop");
  const path = url.searchParams.get("path_prefix") || "";
  const prepath = url.searchParams.get("prepath") || "";

  // Extraire l'endpoint depuis le path
  // const pathParts = path.split("/").filter(Boolean);
  // const endpoint = pathParts[0] || "";

  if (!shop) {
    return new Response(JSON.stringify({ error: "Shop parameter required" }), {
      status: 400,
      headers: { "Content-Type": "application/json" },
    });
  }

  try {
    switch (prepath) {
      case "signup":
        return await handleSignupRequest(request, shop);

      case "client-redeem":
        return await handleClientRedeemRequest(request, shop);

      case "create-coupon":
        return await handleCreateCouponRequest(request, shop);

      default:
        return new Response(JSON.stringify({ error: "Endpoint not found" }), {
          status: 404,
          headers: { "Content-Type": "application/json" },
        });
    }
  } catch (error) {
    console.error("Error in API proxy action:", error);
    return new Response("Internal server error", {
      status: 500,
      headers: { "Content-Type": "text/plain" },
    });
    // return json({ error: "Internal server error" }, { status: 500 });
  }
};

/**
 * Handler pour le widget principal (affichage HTML)
 */
async function handleWidgetRequest(request: Request, shop: string) {
  const url = new URL(request.url);
  const customerId = url.searchParams.get("logged_in_customer_id");

  // Récupérer les paramètres de style pour le widget
  const siteSettings = await getSiteSettings(shop);

  // Si pas de client connecté, afficher un widget de connexion
  if (!customerId) {
    const html = `
      <!DOCTYPE html>
      <html lang="fr">
      <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Login</title>
          <style>
              body {
                  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                  margin: 0;
                  padding: 20px;
                  background: #f8f9fa;
              }
              .loyalty-widget {
                  background: white;
                  border-radius: 8px;
                  padding: 24px;
                  text-align: center;
                  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                  max-width: 400px;
                  margin: 0 auto;
              }
              .loyalty-widget h3 {
                  color: ${siteSettings?.widgetColor || '#2e7d32'};
                  margin-bottom: 16px;
              }
              .loyalty-button {
                  background: ${siteSettings?.widgetColor || '#2e7d32'};
                  color: ${siteSettings?.widgetTextColor || 'white'};
                  border: none;
                  padding: 12px 24px;
                  border-radius: 6px;
                  font-size: 16px;
                  cursor: pointer;
                  text-decoration: none;
                  display: inline-block;
                  margin-top: 16px;
              }
              .loyalty-button:hover {
                  background: ${siteSettings?.widgetSecondaryColor || '#1b5e20'};
              }
          </style>
      </head>
      <body>
          <div class="loyalty-widget">
              <h3>Login</h3>
              <p>Login to access your loyalty program.</p>
              <a href="/account/login" class="loyalty-button">Login</a>
          </div>
      </body>
      </html>
    `;

    return new Response(html, {
      headers: { "Content-Type": "text/html" },
    });
  }

  try {
    // Récupérer les données du client
    const customer = await getCustomerByShopifyId(customerId, shop);

    const html = `
      <!DOCTYPE html>
      <html lang="fr">
      <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Loyalty Widget</title>
          <style>
              body {
                  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                  margin: 0;
                  padding: 20px;
                  background: #f8f9fa;
              }
              .loyalty-widget {
                  background: white;
                  border-radius: 8px;
                  padding: 24px;
                  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                  max-width: 400px;
                  margin: 0 auto;
              }
              .loyalty-widget h3 {
                  color: ${siteSettings?.widgetColor || '#2e7d32'};
                  margin-bottom: 16px;
                  text-align: center;
              }
              .points-display {
                  background: #f8f9fa;
                  border-radius: 8px;
                  padding: 20px;
                  text-align: center;
                  margin: 16px 0;
              }
              .points-value {
                  font-size: 32px;
                  font-weight: bold;
                  color: ${siteSettings?.widgetColor || '#2e7d32'};
                  margin-bottom: 4px;
              }
              .points-label {
                  color: #666;
                  font-size: 14px;
              }
              .customer-info {
                  margin: 16px 0;
                  padding: 16px;
                  background: #f8f9fa;
                  border-radius: 6px;
              }
              .customer-info p {
                  margin: 4px 0;
                  font-size: 14px;
              }
              .badge {
                  display: inline-block;
                  padding: 4px 8px;
                  border-radius: 12px;
                  font-size: 12px;
                  font-weight: 600;
                  text-transform: uppercase;
              }
              .badge.member {
                  background: #e8f5e8;
                  color: ${siteSettings?.widgetColor || '#2e7d32'};
              }
              .badge.guest {
                  background: #e3f2fd;
                  color: #1976d2;
              }
              .loyalty-button {
                  background: ${siteSettings?.widgetColor || '#2e7d32'};
                  color: ${siteSettings?.widgetTextColor || 'white'};
                  border: none;
                  padding: 12px 24px;
                  border-radius: 6px;
                  font-size: 16px;
                  cursor: pointer;
                  text-decoration: none;
                  display: block;
                  text-align: center;
                  margin-top: 16px;
                  width: 100%;
                  box-sizing: border-box;
              }
              .loyalty-button:hover {
                  background: ${siteSettings?.widgetSecondaryColor || '#1b5e20'};
              }
          </style>
      </head>
      <body>
          <div class="loyalty-widget">
              <h3>Loyalty Widget</h3>

              ${customer ? `
                  <div class="customer-info">
                      <p><strong>Status:</strong> <span class="badge ${customer.type}">${customer.type === 'member' ? 'Member' : 'Guest'}</span></p>
                      <p><strong>Orders:</strong> ${customer.ordersCount}</p>
                      <p><strong>Total Spent:</strong> ${customer.totalSpent.toFixed(2)}€</p>
                  </div>

                  <div class="points-display">
                      <div class="points-value">${customer.points}</div>
                      <div class="points-label">Available Points</div>
                  </div>

                  <a href="/apps/proxy/loyalty?shop=${shop}&customer_id=${customerId}" class="loyalty-button">
                      See my rewards
                  </a>
              ` : `
                  <p>Unable to load your information.</p>
                  <a href="/account" class="loyalty-button">My Account</a>
              `}
          </div>
      </body>
      </html>
    `;

    return new Response(html, {
      headers: { "Content-Type": "text/html" },
    });
  } catch (error) {
    console.error("Error in app proxy:", error);

    const errorHtml = `
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Error</title>
</head>
<body>
    <div style="padding: 20px; text-align: center;">
        <h3>Error</h3>
        <p>An error occurred while loading the loyalty program.</p>
    </div>
</body>
</html>
    `;

    return new Response(errorHtml, {
      headers: { "Content-Type": "text/html" },
      status: 500,
    });
  }
}

/**
 * Handler pour récupérer les données client (JSON)
 */
async function handleCustomerRequest(request: Request, shop: string) {
  const url = new URL(request.url);
  const customerId = url.searchParams.get("logged_in_customer_id");

  if (!customerId) {
    return new Response(JSON.stringify({
      id: null,
      customerId: null,
      firstName: null,
      lastName: null,
      email: null,
      type: "guest",
      points: 0,
      vipLevel: null,
      totalSpent: 0,
      ordersCount: 0,
      joinedAt: new Date(),
      recentHistory: [],
      referralsCount: 0
    }), {
      headers: { "Content-Type": "application/json" },
    });
  }

  try {
    // D'abord, récupérer le client par son ID Shopify
    const basicCustomer = await getCustomerByShopifyId(customerId, shop);

    if (!basicCustomer) {
      // Si le client n'existe pas dans notre DB, créer un profil guest
      return new Response(JSON.stringify({
        id: null,
        customerId: customerId,
        firstName: null,
        lastName: null,
        email: null,
        type: "guest",
        points: 0,
        vipLevel: null,
        totalSpent: 0,
        ordersCount: 0,
        joinedAt: new Date(),
        recentHistory: [],
        referralsCount: 0
      }), {
        headers: { "Content-Type": "application/json" },
      });
    }

    // Récupérer les données complètes avec relations
    const customer = await getCustomerById(basicCustomer.id, shop);

    if (!customer) {
      return new Response("Customer not found", {
        status: 404,
        headers: { "Content-Type": "text/plain" },
      });
      // return json({ error: "Customer not found" }, { status: 404 });
    }

    // Calculer les statistiques supplémentaires
    const recentHistory = customer.history ? customer.history.slice(0, 5) : [];
    const referralsCount = customer.referrals ? customer.referrals.filter((r: any) => r.status === "completed").length : 0;

    // Retourner les données complètes pour l'embed
    const customerData = {
      id: customer.id,
      customerId: customer.customerId,
      firstName: customer.firstName,
      lastName: customer.lastName,
      email: customer.email,
      type: customer.type,
      points: customer.points,
      vipLevel: customer.vipLevel,
      totalSpent: customer.totalSpent,
      ordersCount: customer.ordersCount,
      joinedAt: customer.joinedAt,
      recentHistory: recentHistory.map((h: any) => ({
        action: h.action,
        points: h.points,
        description: h.description,
        timestamp: h.timestamp
      })),
      referralsCount
    };

    return new Response(JSON.stringify(customerData), {
      headers: { "Content-Type": "application/json" },
    });
    // return json(customerData);
  } catch (error) {
    console.error("Error fetching customer data:", error);
    return new Response(JSON.stringify({ error: "Internal server error" }), {
      status: 500,
      headers: { "Content-Type": "application/json" },
    });
  }
}

/**
 * Handler pour récupérer les façons de gagner des points
 */
async function handleWaysToEarnRequest(_request: Request, shop: string) {
  try {
    // Récupérer les façons de gagner des points actives
    const waysToEarn = await getWaysToEarn(shop);

    // Filtrer seulement les actives et formater pour l'interface client
    const activeWays = waysToEarn
      .filter(way => way.isActive)
      .map(way => ({
        id: way.id,
        name: way.name,
        description: way.description,
        actionType: way.actionType,
        earningType: way.earningType,
        earningValue: way.earningValue,
        icon: way.icon
      }));

    return new Response(JSON.stringify(activeWays), {
      headers: { "Content-Type": "application/json" },
    });
  } catch (error) {
    console.error("Error fetching ways to earn:", error);
    return new Response(JSON.stringify({ error: "Internal server error" }), {
      status: 500,
      headers: { "Content-Type": "application/json" },
    });
  }
}

/**
 * Handler pour récupérer les façons d'échanger des points
 */
async function handleWaysToRedeemRequest(_request: Request, shop: string) {
  try {
    // Récupérer les façons d'échanger des points actives
    const waysToRedeem = await getWaysToRedeem(shop);

    // Filtrer seulement les actives et trier par coût croissant
    const activeWays = waysToRedeem
      .filter(way => way.isActive)
      .sort((a, b) => a.pointsCost - b.pointsCost); // Trier par coût croissant

    return new Response(JSON.stringify(activeWays), {
      headers: { "Content-Type": "application/json" },
    });
  } catch (error) {
    console.error("Error fetching ways to redeem:", error);
    return new Response(JSON.stringify({ error: "Internal server error" }), {
      status: 500,
      headers: { "Content-Type": "application/json" },
    });
  }
}

/**
 * Handler pour l'inscription au programme de fidélité
 */
async function handleSignupRequest(request: Request, shop: string) {
  const url = new URL(request.url);
  const customerId = url.searchParams.get("logged_in_customer_id");
  const referralCode = url.searchParams.get("referralCode"); // Code de parrainage envoyé depuis le client

  if (!customerId) {
    return new Response(JSON.stringify({ error: "Customer ID required" }), {
      status: 400,
      headers: { "Content-Type": "application/json" },
    });
  }

  try {
    // Essayer de promouvoir le client en membre
    let customer = await promoteToMember(customerId, shop);

    // Si le client n'existe pas, le créer
    if (!customer) {
      customer = await createCustomer({
        customerId: customerId,
        shop: shop,
        type: "member"
      });

      if (!customer) {
        return new Response(JSON.stringify({ error: "Failed to create customer" }), {
          status: 500,
          headers: { "Content-Type": "application/json" },
        });
      }
    }

    // Traiter le code de parrainage si présent (attribution immédiate au filleul)
    let referralProcessed = false;
    if (referralCode) {
      try {
        const result = await processReferralSignup(shop, referralCode, customerId);
        if (result.success) {
          referralProcessed = true;
          console.log(`Parrainage d'inscription traité avec succès pour ${customerId}`);
        } else {
          console.log(`Échec du traitement du parrainage d'inscription: ${result.error}`);
        }
      } catch (error) {
        console.error("Error processing referral during signup:", error);
      }
    }

    // Attribuer les points de bienvenue
    const signupResult = await awardPointsForSignup(shop, customerId);

    // Récupérer le client mis à jour après l'attribution des points
    const updatedCustomer = await getCustomerByShopifyId(customerId, shop);
    if (!updatedCustomer) {
      return new Response(JSON.stringify({ error: "Failed to retrieve updated customer" }), {
        status: 500,
        headers: { "Content-Type": "application/json" },
      });
    }

    // Générer automatiquement un lien de parrainage pour le nouveau membre
    let referralLink = null;
    try {
      const referralResult = await createReferralLink(shop, customerId);
      if (referralResult.success) {
        referralLink = referralResult.referralUrl;
      }
    } catch (error) {
      console.error("Error creating referral link during signup:", error);
      // Ne pas faire échouer l'inscription si la génération du lien échoue
    }

    const response = {
      success: true,
      customer: {
        id: updatedCustomer.id,
        customerId: updatedCustomer.customerId,
        firstName: updatedCustomer.firstName,
        lastName: updatedCustomer.lastName,
        email: updatedCustomer.email,
        type: updatedCustomer.type,
        points: updatedCustomer.points,
        totalSpent: updatedCustomer.totalSpent,
        ordersCount: updatedCustomer.ordersCount
      },
      pointsAwarded: signupResult?.points || 0,
      referralLink: referralLink,
      referralProcessed: referralProcessed,
      message: `Welcome to our loyalty program! You have received ${signupResult?.points || 0} welcome points.${referralProcessed ? ' Your referral has been validated !' : ''}`
    };

    return new Response(JSON.stringify(response), {
      headers: { "Content-Type": "application/json" },
    });
  } catch (error) {
    console.error("Error during loyalty signup:", error);
    return new Response(JSON.stringify({ error: "Internal server error" }), {
      status: 500,
      headers: { "Content-Type": "application/json" },
    });
  }
}

/**
 * Handler pour récupérer les informations du programme
 */
async function handleProgramInfoRequest(_request: Request, shop: string) {
  try {
    // Récupérer les settings du programme et les settings complets
    const [programSettings, settings, siteSettings, referralSettings] = await Promise.all([
      getProgramSettings(),
      prisma.settings.findUnique({
        where: { shop }
        // Récupérer TOUS les paramètres pour l'interface client
      }),
      getSiteSettings(shop), // Récupérer les paramètres de style
      getReferralSettings(shop) // Récupérer les paramètres de parrainage
    ]);

    const programInfo = {
      isActive: programSettings?.status || false,
      name: programSettings?.name || "Programme de fidélité",
      description: programSettings?.description || "Gain points for every purchase !",
      widgetEnabled: settings?.widgetEnabled !== false,

      // Paramètres de style complets
      primaryColor: settings?.primaryColor || "#2E7D32",
      language: settings?.language || "fr",

      // Paramètres de style depuis SiteSettings (vrais paramètres configurés)
      widgetColor: siteSettings?.widgetColor || settings?.primaryColor || "#2E7D32",
      widgetSecondaryColor: siteSettings?.widgetSecondaryColor || "#4CAF50",
      widgetTextColor: siteSettings?.widgetTextColor || "#FFFFFF",
      widgetPosition: siteSettings?.widgetPosition || "bottom-right",
      widgetSize: siteSettings?.widgetSize || "medium",
      widgetBorderRadius: siteSettings?.widgetBorderRadius || "rounded",
      widgetShadow: siteSettings?.widgetShadow !== false,
      widgetAnimation: siteSettings?.widgetAnimation !== false,
      showPointsOnButton: siteSettings?.showPointsOnButton !== false,

      // Autres paramètres configurés
      pointsName: siteSettings?.pointsName || "Points",
      welcomeMessage: siteSettings?.welcomeMessage || "Welcome to our loyalty program!",
      shopName: siteSettings?.shopName || shop,
      currency: siteSettings?.currency || "EUR",
      customCSS: siteSettings?.customCSS || "",

      // Paramètres de conversion des points
      redemptionRate: settings?.redemptionRate || 100, // 100 points = 1€
      earningRate: settings?.earningRate || 1, // 1 point par euro dépensé

      // Paramètres de parrainage
      referralActive: referralSettings?.active || false,
      referralSettings: referralSettings ? {
        referrerReward: referralSettings.referrerReward,
        referredReward: referralSettings.referredReward,
        minimumPurchase: referralSettings.minimumPurchase
      } : null
    };
    return new Response(JSON.stringify(programInfo), {
      headers: { "Content-Type": "application/json" },
    });
    // return json(programInfo);
  } catch (error) {
    console.error("Error fetching program info:", error);
    return new Response("Internal server error", {
      status: 500,
      headers: { "Content-Type": "text/plain" },
    });
    // return json({ error: "Internal server error" }, { status: 500 });
  }
}

/**
 * Handler pour récupérer les produits échangeables côté client
 */
async function handleClientProductsRequest(request: Request, shop: string) {
  try {
    // Récupérer les produits échangeables et les paramètres du programme
    const [exchangeableProducts, pointsSettings, siteSettings] = await Promise.all([
      getExchangeableProducts(shop),
      getPointsSettings(shop),
      getSiteSettings(shop)
    ]);

    // Calculer le coût en points basé sur les paramètres du programme
    const productsWithPoints = exchangeableProducts
      .filter(product => product.active) // Seulement les produits actifs
      .map(product => ({
        ...product,
        pointsCost: pointsSettings?.redemptionRate
          ? Math.ceil(parseFloat(product.price) * pointsSettings.redemptionRate)
          : product.pointsCost || 100
      }));

      return new Response(JSON.stringify({
        products: productsWithPoints,
        programInfo: {
          pointsName: siteSettings?.pointsName || 'Points',
          redemptionRate: pointsSettings?.redemptionRate || 100
        }
      }), {
        headers: { "Content-Type": "application/json" },
      });
    // return json({
    //   products: productsWithPoints,
    //   programInfo: {
    //     pointsName: siteSettings?.pointsName || 'Points',
    //     redemptionRate: pointsSettings?.redemptionRate || 100
    //   }
    // });
  } catch (error) {
    console.error("Error loading client products:", error);
    return new Response(JSON.stringify({
      products: [],
      programInfo: { pointsName: 'Points', redemptionRate: 100 },
      error: "Error loading products"
    }), {
      status: 500,
      headers: { "Content-Type": "application/json" },
    });
    // return json({
    //   products: [],
    //   programInfo: { pointsName: 'Points', redemptionRate: 100 },
    //   error: "Error loading products"
    // }, { status: 500 });
  }
}

/**
 * Handler pour l'échange de récompenses côté client
 */
async function handleClientRedeemRequest(request: Request, shop: string) {
  try {
    const formData = await request.formData();
    const customerId = formData.get('customerId') as string;
    const wayToRedeemId = formData.get('wayToRedeemId') as string;
    const pointsToSpend = parseInt(formData.get('pointsToSpend') as string);
    const redeemType = formData.get('redeemType') as string;
    const redeemValue = parseFloat(formData.get('redeemValue') as string);

    if (!customerId || !wayToRedeemId || !pointsToSpend || !redeemType) {
      return new Response(JSON.stringify({ error: "Paramètres manquants" }), {
        status: 400,
        headers: { "Content-Type": "application/json" },
      });
    }

    // Récupérer les données du client
    const customerData = await getCustomerByShopifyId(customerId, shop);
    if (!customerData) {
      return new Response(JSON.stringify({ error: "Client non trouvé" }), {
        status: 404,
        headers: { "Content-Type": "application/json" },
      });
    }

    // Vérifier si le client a assez de points
    if (customerData.points < pointsToSpend) {
      return new Response(JSON.stringify({
        error: "Points insuffisants",
        required: pointsToSpend,
        available: customerData.points
      }), {
        status: 400,
        headers: { "Content-Type": "application/json" },
      });
    }

    // Récupérer la façon d'échanger pour validation
    const waysToRedeem = await getWaysToRedeem(shop);
    const wayToRedeem = waysToRedeem.find(w => w.id === wayToRedeemId);

    if (!wayToRedeem || !wayToRedeem.isActive) {
      return new Response(JSON.stringify({ error: "Méthode d'échange non trouvée ou inactive" }), {
        status: 404,
        headers: { "Content-Type": "application/json" },
      });
    }

    // Validation selon le type de récompense
    if (redeemType === 'coupon' && wayToRedeem.isConfigurable) {
      // Pour les coupons configurables, valider les limites min/max
      const minPoints = wayToRedeem.minPoints || 100;
      const maxPoints = wayToRedeem.maxPoints || 10000;

      if (pointsToSpend < minPoints || pointsToSpend > maxPoints) {
        return new Response(JSON.stringify({
          error: `Points invalides. Minimum: ${minPoints}, Maximum: ${maxPoints}`
        }), {
          status: 400,
          headers: { "Content-Type": "application/json" },
        });
      }
    } else {
      // Pour les autres types, valider le coût fixe
      if (wayToRedeem.pointsCost !== pointsToSpend) {
        return new Response(JSON.stringify({ error: "Coût en points incorrect" }), {
          status: 400,
          headers: { "Content-Type": "application/json" },
        });
      }
    }

    let discountResult;

    // Créer le code de réduction selon le type
    switch (redeemType) {
      case 'discount':
        // Créer un code de réduction fixe
        const { createLoyaltyDiscountCode } = await import("../services/shopifyDiscounts.server");
        const pointsSettings = await getPointsSettings(shop);
        const redemptionRate = pointsSettings?.redemptionRate || 100; // 100 points = 1€

        discountResult = await createLoyaltyDiscountCode(
          shop, // Utiliser shop au lieu de request
          pointsToSpend,
          redemptionRate,
          30, // 30 jours d'expiration
          customerId // Passer l'ID du client
        );
        break;

      case 'shipping':
        // Créer un code de livraison gratuite
        const { createFreeShippingWithStoredToken } = await import("../services/shopifyAuth.server");
        const shippingCode = `LOYALTYSHIP${Date.now().toString(36).toUpperCase()}`;
        const shippingExpiresAt = new Date();
        shippingExpiresAt.setDate(shippingExpiresAt.getDate() + 30);

        discountResult = await createFreeShippingWithStoredToken(shop, {
          title: `Loyalty Free Shipping - ${shippingCode}`,
          code: shippingCode,
          expiresAt: shippingExpiresAt,
          usageLimit: 1,
          minimumAmount: 0,
          customerId: customerId
        });
        break;

      case 'product':
        // Pour les produits, créer un code de réduction de 100% pour le produit spécifique
        // TODO: Implémenter la logique spécifique aux produits
        discountResult = {
          success: true,
          code: `PRODUCT${Date.now()}`,
          message: "Code produit généré (à implémenter)"
        };
        break;

      case 'coupon':
        // Coupon configurable - récupérer le taux de conversion depuis la base
        const { createLoyaltyDiscountCode: createCouponCode } = await import("../services/shopifyDiscounts.server");
        const couponPointsSettings = await getPointsSettings(shop);
        const couponRedemptionRate = couponPointsSettings?.redemptionRate || 100; // 100 points = 1€

        discountResult = await createCouponCode(
          shop, // Utiliser shop au lieu de request
          pointsToSpend,
          couponRedemptionRate, // Utiliser le taux de la base de données
          30, // 30 jours d'expiration
          customerId // Passer l'ID du client
        );
        break;

      default:
        return new Response(JSON.stringify({ error: `Type d'échange "${redeemType}" non supporté` }), {
          status: 400,
          headers: { "Content-Type": "application/json" },
        });
    }

    if (!discountResult.success) {
      return new Response(JSON.stringify({ error: discountResult.error || "Erreur lors de la création du code" }), {
        status: 500,
        headers: { "Content-Type": "application/json" },
      });
    }

    // Déduire les points
    const newPoints = customerData.points - pointsToSpend;
    await updateCustomerPoints(customerId, shop, newPoints);

    // Enregistrer dans l'historique
    await prisma.pointsHistory.create({
      data: {
        ledgerId: customerData.id, // Utiliser ledgerId au lieu de customerId
        action: 'redeem',
        points: -pointsToSpend,
        description: `Exchange: ${wayToRedeem.name}`,
        metadata: JSON.stringify({ // Convertir en JSON string
          wayToRedeemId,
          redeemType,
          discountCode: discountResult.code,
          discountId: discountResult.discountId
        })
      }
    });

    // Enregistrer la récompense dans la table Reward
    const pointsSettings = await getPointsSettings(shop);
    await prisma.reward.create({
      data: {
        customerId: customerData.id,
        shopifyCustomerId: customerData.customerId,
        shop: shop,
        type: redeemType,
        name: wayToRedeem.name,
        value: redeemValue || (pointsToSpend / (pointsSettings?.redemptionRate || 100)),
        pointsCost: pointsToSpend,
        code: discountResult.code,
        discountId: discountResult.discountId,
        status: 'active',
        expiresAt: wayToRedeem.expiryDays ? new Date(Date.now() + wayToRedeem.expiryDays * 24 * 60 * 60 * 1000) : null
      }
    });

    return new Response(JSON.stringify({
      success: true,
      pointsUsed: pointsToSpend,
      remainingPoints: newPoints,
      redeemType: redeemType,
      code: discountResult.code,
      discountId: discountResult.discountId
    }), {
      headers: { "Content-Type": "application/json" },
    });

  } catch (error) {
    console.error("Error redeeming reward:", error);
    return new Response(JSON.stringify({ error: "Erreur lors de l'échange" }), {
      status: 500,
      headers: { "Content-Type": "application/json" },
    });
  }
}

/**
 * Handler pour calculer la valeur d'un coupon
 */
async function handleCouponValueRequest(request: Request, shop: string) {
  try {
    const url = new URL(request.url);
    const points = url.searchParams.get('points');

    if (!points) {
      return new Response(JSON.stringify({ error: "Paramètre points manquant" }), {
        status: 400,
        headers: { "Content-Type": "application/json" },
      });
    }

    const pointsNumber = parseInt(points);
    const couponValue = await calculateCouponValue(shop, pointsNumber);

    return new Response(JSON.stringify({
      points: pointsNumber,
      value: couponValue,
      formatted: `${couponValue}€`
    }), {
      headers: { "Content-Type": "application/json" },
    });

  } catch (error) {
    console.error("Error calculating coupon value:", error);
    return new Response(JSON.stringify({ error: "Erreur lors du calcul" }), {
      status: 500,
      headers: { "Content-Type": "application/json" },
    });
  }
}

/**
 * Handler pour créer un coupon de fidélité
 */
async function handleCreateCouponRequest(request: Request, shop: string) {
  try {
    const formData = await request.formData();
    const customerId = formData.get('customerId') as string;
    const pointsToSpend = parseInt(formData.get('pointsToSpend') as string);
    const wayToRedeemId = formData.get('wayToRedeemId') as string;

    if (!customerId || !pointsToSpend) {
      return new Response(JSON.stringify({ error: "Paramètres manquants" }), {
        status: 400,
        headers: { "Content-Type": "application/json" },
      });
    }

    // Créer le coupon de fidélité
    const result = await createLoyaltyCoupon(request, {
      customerId,
      shop,
      pointsToSpend,
      wayToRedeemId
    });

    if (!result.success) {
      return new Response(JSON.stringify({ error: result.error }), {
        status: 400,
        headers: { "Content-Type": "application/json" },
      });
    }

    return new Response(JSON.stringify({
      success: true,
      coupon: result.coupon,
      message: `Coupon de ${result.coupon?.value}€ créé avec succès !`
    }), {
      headers: { "Content-Type": "application/json" },
    });

  } catch (error) {
    console.error("Error creating coupon:", error);
    return new Response(JSON.stringify({ error: "Erreur lors de la création du coupon" }), {
      status: 500,
      headers: { "Content-Type": "application/json" },
    });
  }
}

/**
 * Handler pour récupérer les récompenses du client
 */
async function handleCustomerRewardsRequest(request: Request, shop: string) {
  const url = new URL(request.url);
  const customerId = url.searchParams.get("customerId");

  if (!customerId) {
    return new Response(JSON.stringify({ error: "Customer ID required" }), {
      status: 400,
      headers: { "Content-Type": "application/json" },
    });
  }

  try {
    // Récupérer le client d'abord
    const customer = await getCustomerByShopifyId(customerId, shop);
    if (!customer) {
      return new Response(JSON.stringify({
        availableRewards: [],
        pastRewards: []
      }), {
        headers: { "Content-Type": "application/json" },
      });
    }

    // Récupérer les récompenses disponibles (non utilisées et non expirées)
    const availableRewards = await prisma.reward.findMany({
      where: {
        customerId: customer.id,
        status: 'active',
        OR: [
          { expiresAt: null },
          { expiresAt: { gt: new Date() } }
        ]
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    // Récupérer les récompenses passées (utilisées)
    const pastRewards = await prisma.reward.findMany({
      where: {
        customerId: customer.id,
        status: 'used'
      },
      orderBy: {
        usedAt: 'desc'
      },
      take: 10 // Limiter à 10 dernières récompenses
    });

    // Formater les données
    const formattedAvailable = availableRewards.map(reward => ({
      id: reward.id,
      title: reward.name,
      subtitle: `Spent ${reward.pointsCost} Points`,
      type: reward.type,
      status: 'available',
      code: reward.code,
      value: reward.value,
      expiresAt: reward.expiresAt
    }));

    const formattedPast = pastRewards.map(reward => ({
      id: reward.id,
      title: reward.name,
      subtitle: `Used on ${reward.usedAt?.toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' })}`,
      status: 'used',
      code: reward.code,
      value: reward.value,
      usedAt: reward.usedAt
    }));

    return new Response(JSON.stringify({
      availableRewards: formattedAvailable,
      pastRewards: formattedPast
    }), {
      headers: { "Content-Type": "application/json" },
    });
  } catch (error) {
    console.error('Error fetching rewards:', error);
    return new Response(JSON.stringify({ error: "Internal server error" }), {
      status: 500,
      headers: { "Content-Type": "application/json" },
    });
  }
}

/**
 * Handler pour récupérer l'historique du client
 */
async function handleCustomerHistoryRequest(request: Request, shop: string) {
  const url = new URL(request.url);
  const customerId = url.searchParams.get("customerId");

  if (!customerId) {
    return new Response(JSON.stringify({ error: "Customer ID required" }), {
      status: 400,
      headers: { "Content-Type": "application/json" },
    });
  }

  try {
    // Récupérer le client d'abord
    const customer = await getCustomerByShopifyId(customerId, shop);
    if (!customer) {
      return new Response(JSON.stringify({
        history: []
      }), {
        headers: { "Content-Type": "application/json" },
      });
    }

    const history = await prisma.pointsHistory.findMany({
      where: {
        ledgerId: customer.id
      },
      orderBy: {
        timestamp: 'desc'
      },
      take: 50 // Limiter à 50 dernières activités
    });

    return new Response(JSON.stringify({
      history: history.map(entry => ({
        id: entry.id,
        description: entry.description || entry.action,
        points: entry.points,
        createdAt: entry.timestamp,
        type: entry.points > 0 ? 'earned' : 'spent'
      }))
    }), {
      headers: { "Content-Type": "application/json" },
    });
  } catch (error) {
    console.error('Error fetching history:', error);
    return new Response(JSON.stringify({ error: "Internal server error" }), {
      status: 500,
      headers: { "Content-Type": "application/json" },
    });
  }
}

/**
 * Handler pour récupérer les parrainages du client
 */
async function handleCustomerReferralsRequest(request: Request, shop: string) {
  const url = new URL(request.url);
  const customerId = url.searchParams.get("customerId");

  if (!customerId) {
    return new Response(JSON.stringify({ error: "Customer ID required" }), {
      status: 400,
      headers: { "Content-Type": "application/json" },
    });
  }

  try {
    // Utiliser le nouveau service pour récupérer les parrainages
    const referrals = await getCustomerReferrals(shop, customerId);

    return new Response(JSON.stringify({
      referrals: referrals.map(referral => ({
        id: referral.id,
        referredEmail: referral.referredEmail || 'Unknown',
        status: referral.status,
        pointsEarned: referral.pointsEarned || 0,
        createdAt: referral.createdAt
      }))
    }), {
      headers: { "Content-Type": "application/json" },
    });
  } catch (error) {
    console.error('Error fetching referrals:', error);
    return new Response(JSON.stringify({ error: "Internal server error" }), {
      status: 500,
      headers: { "Content-Type": "application/json" },
    });
  }
}

/**
 * Handler pour générer un lien de parrainage
 */
async function handleGenerateReferralRequest(request: Request, shop: string) {
  const url = new URL(request.url);
  const customerId = url.searchParams.get("customerId");

  if (!customerId) {
    return new Response(JSON.stringify({ error: "Customer ID required" }), {
      status: 400,
      headers: { "Content-Type": "application/json" },
    });
  }

  try {
    const result = await createReferralLink(shop, customerId);

    if (!result.success) {
      return new Response(JSON.stringify({ error: result.error }), {
        status: 400,
        headers: { "Content-Type": "application/json" },
      });
    }

    return new Response(JSON.stringify({
      success: true,
      referralUrl: result.referralUrl,
      code: result.code
    }), {
      headers: { "Content-Type": "application/json" },
    });
  } catch (error) {
    console.error('Error generating referral link:', error);
    return new Response(JSON.stringify({ error: "Internal server error" }), {
      status: 500,
      headers: { "Content-Type": "application/json" },
    });
  }
}
