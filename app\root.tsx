import {
  <PERSON><PERSON>,
  <PERSON>a,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  ScrollRestoration,
} from "@remix-run/react";

export default function App() {
  return (
    <html lang="fr">
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width,initial-scale=1" />
        <meta httpEquiv="Content-Security-Policy" content="frame-src 'self' https://*.shopify.com https://*.myshopify.com; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://*.shopify.com https://*.myshopify.com https://cdn.shopify.com; connect-src 'self' https://*.shopify.com https://*.myshopify.com;" />
        <link rel="preconnect" href="https://cdn.shopify.com/" crossOrigin="anonymous" />
        <link
          rel="stylesheet"
          href="https://cdn.shopify.com/static/fonts/inter/v4/styles.css"
          crossOrigin="anonymous"
        />
        <Meta />
        <Links />
      </head>
      <body>
        <Outlet />
        <ScrollRestoration />
        <Scripts />
      </body>
    </html>
  );
}
