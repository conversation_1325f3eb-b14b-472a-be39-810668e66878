/*
  Warnings:

  - You are about to drop the column `refereeId` on the `Referral` table. All the data in the column will be lost.
  - You are about to drop the column `vipThreshold` on the `Settings` table. All the data in the column will be lost.
  - Added the required column `expiresAt` to the `Referral` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE "PointsHistory" ADD COLUMN "expiresAt" DATETIME;

-- CreateTable
CREATE TABLE "VIPLevel" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "shop" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "threshold" REAL NOT NULL,
    "pointsMultiplier" REAL NOT NULL DEFAULT 1,
    "benefits" TEXT NOT NULL,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    CONSTRAINT "VIPLevel_shop_fkey" FOREIGN KEY ("shop") REFERENCES "Settings" ("shop") ON DELETE RESTRICT ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "Campaign" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "shop" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "value" REAL NOT NULL,
    "target" TEXT NOT NULL,
    "targetIds" TEXT,
    "startDate" DATETIME NOT NULL,
    "endDate" DATETIME NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'draft',
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    CONSTRAINT "Campaign_shop_fkey" FOREIGN KEY ("shop") REFERENCES "Settings" ("shop") ON DELETE RESTRICT ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "ReferralSettings" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "shop" TEXT NOT NULL,
    "active" BOOLEAN NOT NULL DEFAULT false,
    "referrerReward" TEXT NOT NULL,
    "referredReward" TEXT NOT NULL,
    "minimumPurchase" REAL NOT NULL DEFAULT 0,
    "expiryDays" INTEGER NOT NULL DEFAULT 30,
    "customMessage" TEXT,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL
);

-- RedefineTables
PRAGMA defer_foreign_keys=ON;
PRAGMA foreign_keys=OFF;
CREATE TABLE "new_PointsLedger" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "customerId" TEXT NOT NULL,
    "shop" TEXT NOT NULL,
    "points" INTEGER NOT NULL DEFAULT 0,
    "vipLevel" TEXT,
    "lastUpdated" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "PointsLedger_shop_fkey" FOREIGN KEY ("shop") REFERENCES "Settings" ("shop") ON DELETE RESTRICT ON UPDATE CASCADE
);
INSERT INTO "new_PointsLedger" ("customerId", "id", "lastUpdated", "points", "shop") SELECT "customerId", "id", "lastUpdated", "points", "shop" FROM "PointsLedger";
DROP TABLE "PointsLedger";
ALTER TABLE "new_PointsLedger" RENAME TO "PointsLedger";
CREATE INDEX "PointsLedger_shop_idx" ON "PointsLedger"("shop");
CREATE UNIQUE INDEX "PointsLedger_customerId_shop_key" ON "PointsLedger"("customerId", "shop");
CREATE TABLE "new_Referral" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "shop" TEXT NOT NULL,
    "referrerId" TEXT NOT NULL,
    "referredId" TEXT,
    "code" TEXT NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'pending',
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "completedAt" DATETIME,
    "expiresAt" DATETIME NOT NULL,
    CONSTRAINT "Referral_referrerId_fkey" FOREIGN KEY ("referrerId") REFERENCES "PointsLedger" ("id") ON DELETE RESTRICT ON UPDATE CASCADE,
    CONSTRAINT "Referral_referredId_fkey" FOREIGN KEY ("referredId") REFERENCES "PointsLedger" ("id") ON DELETE SET NULL ON UPDATE CASCADE
);
INSERT INTO "new_Referral" ("code", "completedAt", "createdAt", "id", "referrerId", "shop", "status") SELECT "code", "completedAt", "createdAt", "id", "referrerId", "shop", "status" FROM "Referral";
DROP TABLE "Referral";
ALTER TABLE "new_Referral" RENAME TO "Referral";
CREATE UNIQUE INDEX "Referral_code_key" ON "Referral"("code");
CREATE INDEX "Referral_shop_idx" ON "Referral"("shop");
CREATE INDEX "Referral_referrerId_idx" ON "Referral"("referrerId");
CREATE INDEX "Referral_code_idx" ON "Referral"("code");
CREATE TABLE "new_Settings" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "shop" TEXT NOT NULL,
    "earningRate" REAL NOT NULL DEFAULT 1.0,
    "redemptionRate" REAL NOT NULL DEFAULT 0.01,
    "minimumPoints" INTEGER NOT NULL DEFAULT 100,
    "referralPoints" INTEGER NOT NULL DEFAULT 100,
    "birthdayPoints" INTEGER NOT NULL DEFAULT 250,
    "widgetEnabled" BOOLEAN NOT NULL DEFAULT true,
    "primaryColor" TEXT NOT NULL DEFAULT '#000000',
    "language" TEXT NOT NULL DEFAULT 'fr'
);
INSERT INTO "new_Settings" ("birthdayPoints", "earningRate", "id", "language", "primaryColor", "redemptionRate", "referralPoints", "shop", "widgetEnabled") SELECT "birthdayPoints", "earningRate", "id", "language", "primaryColor", "redemptionRate", "referralPoints", "shop", "widgetEnabled" FROM "Settings";
DROP TABLE "Settings";
ALTER TABLE "new_Settings" RENAME TO "Settings";
CREATE UNIQUE INDEX "Settings_shop_key" ON "Settings"("shop");
PRAGMA foreign_keys=ON;
PRAGMA defer_foreign_keys=OFF;

-- CreateIndex
CREATE INDEX "VIPLevel_shop_idx" ON "VIPLevel"("shop");

-- CreateIndex
CREATE UNIQUE INDEX "VIPLevel_shop_name_key" ON "VIPLevel"("shop", "name");

-- CreateIndex
CREATE INDEX "Campaign_shop_idx" ON "Campaign"("shop");

-- CreateIndex
CREATE INDEX "Campaign_status_idx" ON "Campaign"("status");

-- CreateIndex
CREATE UNIQUE INDEX "ReferralSettings_shop_key" ON "ReferralSettings"("shop");
