/**
 * Test simple pour vérifier que les modèles fonctionnent
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  console.log('🧪 Test simple de connexion à la base de données...');
  
  try {
    // Vérifier la connexion
    await prisma.$connect();
    console.log('✅ Connexion à la base de données réussie');
    
    // Compter les commandes existantes
    const orderCount = await prisma.order.count();
    console.log(`📊 Nombre de commandes en base: ${orderCount}`);
    
    // Compter les clients existants
    const customerCount = await prisma.customer.count();
    console.log(`👥 Nombre de clients en base: ${customerCount}`);
    
    // Compter les paramètres existants
    const settingsCount = await prisma.settings.count();
    console.log(`⚙️ Nombre de paramètres en base: ${settingsCount}`);
    
    // Lister quelques commandes récentes
    const recentOrders = await prisma.order.findMany({
      take: 3,
      orderBy: { createdAt: 'desc' },
      include: {
        customer: {
          select: {
            firstName: true,
            lastName: true,
            email: true
          }
        }
      }
    });
    
    if (recentOrders.length > 0) {
      console.log('\n📋 Commandes récentes:');
      recentOrders.forEach(order => {
        console.log(`   - ${order.orderId}: ${order.total}€ (${order.customer.firstName} ${order.customer.lastName})`);
      });
    } else {
      console.log('\n📋 Aucune commande trouvée en base de données');
    }
    
  } catch (error) {
    console.error('❌ Erreur:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
