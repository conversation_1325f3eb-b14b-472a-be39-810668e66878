/**
 * Script de test pour le système de redemption
 */

import { PrismaClient } from '@prisma/client';
const prisma = new PrismaClient();

async function testRedemptionSystem() {
  try {
    console.log('🧪 Test du système de redemption...\n');

    // 1. Vérifier les ways to redeem
    console.log('1️⃣ Vérification des ways to redeem...');
    const waysToRedeem = await prisma.wayToRedeem.findMany({
      where: { isActive: true }
    });
    
    console.log(`✅ ${waysToRedeem.length} ways to redeem actives trouvées:`);
    waysToRedeem.forEach(way => {
      console.log(`   - ${way.name} (${way.redeemType}): ${way.pointsCost} points = €${way.redeemValue}`);
      if (way.isConfigurable) {
        console.log(`     Configurable: ${way.minPoints}-${way.maxPoints} points`);
      }
    });

    // 2. Vérifier les paramètres de points
    console.log('\n2️⃣ Vérification des paramètres de points...');
    const settings = await prisma.settings.findFirst();
    if (settings) {
      console.log(`✅ Paramètres trouvés:`);
      console.log(`   - Taux de conversion: ${settings.redemptionRate} points/€`);
      console.log(`   - Taux de gain: ${settings.earningRate} points/€`);
      console.log(`   - Points minimum: ${settings.minimumPoints}`);
      
      // Test de calcul
      const testPoints = 1000;
      const testValue = testPoints / settings.redemptionRate;
      console.log(`   - Test: ${testPoints} points = €${testValue.toFixed(2)}`);
    }

    // 3. Vérifier le client de test
    console.log('\n3️⃣ Vérification du client de test...');
    const testCustomer = await prisma.customer.findFirst({
      where: { email: "<EMAIL>" }
    });
    
    if (testCustomer) {
      console.log(`✅ Client de test trouvé:`);
      console.log(`   - Email: ${testCustomer.email}`);
      console.log(`   - Points: ${testCustomer.points}`);
      console.log(`   - Type: ${testCustomer.type}`);
      console.log(`   - Commandes: ${testCustomer.ordersCount}`);
    }

    // 4. Test de calcul de coût en points
    console.log('\n4️⃣ Test de calcul de coût en points...');
    if (settings) {
      const testProducts = [
        { name: "Produit 10€", price: 10 },
        { name: "Produit 25€", price: 25 },
        { name: "Produit 50€", price: 50 }
      ];

      testProducts.forEach(product => {
        const pointsCost = Math.ceil(product.price * settings.redemptionRate);
        console.log(`   - ${product.name}: ${pointsCost} points`);
      });
    }

    // 5. Vérifier les coupons existants
    console.log('\n5️⃣ Vérification des coupons existants...');
    const existingRewards = await prisma.reward.findMany({
      where: { type: "discount" },
      orderBy: { createdAt: "desc" },
      take: 5
    });

    if (existingRewards.length > 0) {
      console.log(`✅ ${existingRewards.length} coupons trouvés (5 derniers):`);
      existingRewards.forEach(reward => {
        console.log(`   - ${reward.name}: ${reward.code} (€${reward.value})`);
        console.log(`     Status: ${reward.status}, Expire: ${reward.expiresAt?.toLocaleDateString()}`);
      });
    } else {
      console.log('ℹ️ Aucun coupon trouvé (normal pour un nouveau système)');
    }

    // 6. Test de validation des limites
    console.log('\n6️⃣ Test de validation des limites...');
    const configurableWay = waysToRedeem.find(way => way.isConfigurable);
    if (configurableWay && testCustomer) {
      console.log(`✅ Test avec way configurable: ${configurableWay.name}`);
      console.log(`   - Points client: ${testCustomer.points}`);
      console.log(`   - Limites: ${configurableWay.minPoints}-${configurableWay.maxPoints} points`);
      
      const canUseMin = testCustomer.points >= (configurableWay.minPoints || 0);
      const canUseMax = testCustomer.points >= (configurableWay.maxPoints || 0);
      
      console.log(`   - Peut utiliser minimum: ${canUseMin ? '✅' : '❌'}`);
      console.log(`   - Peut utiliser maximum: ${canUseMax ? '✅' : '❌'}`);
    }

    console.log('\n🎉 Tests terminés avec succès !');
    console.log('\n📋 Résumé:');
    console.log(`   - Ways to redeem: ${waysToRedeem.length}`);
    console.log(`   - Client de test: ${testCustomer ? '✅' : '❌'}`);
    console.log(`   - Paramètres: ${settings ? '✅' : '❌'}`);
    console.log(`   - Coupons existants: ${existingRewards.length}`);

    console.log('\n🚀 Le système est prêt pour les tests manuels !');
    console.log('   1. Ouvrez l\'interface admin pour voir les ways to redeem');
    console.log('   2. Testez le widget client avec le client de test');
    console.log('   3. Essayez de créer des coupons configurables et fixes');

  } catch (error) {
    console.error('❌ Erreur lors des tests:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Fonction pour nettoyer les données de test
async function cleanupTestData() {
  try {
    console.log('🧹 Nettoyage des données de test...');

    // Supprimer les coupons de test
    const deletedRewards = await prisma.reward.deleteMany({
      where: {
        customerId: "test-customer-123"
      }
    });

    // Supprimer l'historique de test
    const deletedHistory = await prisma.pointsHistory.deleteMany({
      where: {
        ledger: {
          customerId: "test-customer-123"
        }
      }
    });

    console.log(`✅ Nettoyage terminé:`);
    console.log(`   - ${deletedRewards.count} coupons supprimés`);
    console.log(`   - ${deletedHistory.count} entrées d'historique supprimées`);

  } catch (error) {
    console.error('❌ Erreur lors du nettoyage:', error);
  }
}

// Exécuter les tests
async function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--cleanup')) {
    await cleanupTestData();
  } else {
    await testRedemptionSystem();
  }
}

main().catch(console.error);

export { testRedemptionSystem, cleanupTestData };
