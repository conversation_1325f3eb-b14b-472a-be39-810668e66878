.languageSelector {
  position: relative;
}

.activatorButton {
  background: rgba(255, 255, 255, 0.15) !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  border-radius: 6px !important;
  padding: 6px 12px !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
  color: white !important;
  min-height: auto !important;
}

.activatorButton:hover {
  background: rgba(255, 255, 255, 0.25) !important;
  border-color: rgba(255, 255, 255, 0.5) !important;
}

.activatorButton:focus {
  outline: 2px solid rgba(255, 255, 255, 0.5) !important;
  outline-offset: 2px !important;
}

.activatorContent {
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
  color: white !important;
  font-weight: 500 !important;
  font-size: 14px !important;
}

.flag {
  font-size: 16px !important;
}

.languageName {
  color: white !important;
  font-weight: 500 !important;
}

.chevron {
  color: white !important;
  font-size: 12px !important;
  margin-left: 4px !important;
}
