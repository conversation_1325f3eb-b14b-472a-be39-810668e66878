import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData, Link } from "@remix-run/react";
import {
  Page,
  Layout,
  Card,
  DataTable,
  Badge,
  Text,
  BlockStack,
  Button,
  ButtonGroup,
  Tabs,
  Grid,
  Icon
} from "@shopify/polaris";
import { useState, useCallback } from "react";
import { ArrowLeftIcon, ViewIcon } from "@shopify/polaris-icons";
import { AdminLayout } from "app/components/Layout/AdminLayout";
import { authenticate } from "app/shopify.server";
import { getCustomerById } from "app/models/Customer.server";
import { useTranslation } from "app/hooks/useTranslation";

export const loader = async ({ request, params }: LoaderFunctionArgs) => {
  const { session } = await authenticate.admin(request);
  const customerId = params.id;

  if (!customerId) {
    throw new Response("Customer ID required", { status: 400 });
  }

  const customer = await getCustomerById(customerId, session.shop);

  if (!customer) {
    throw new Response("Customer not found", { status: 404 });
  }

  return json({ customer, shop: session.shop });
};

export default function CustomerDetail() {
  const { customer, shop } = useLoaderData<typeof loader>();
  const [selectedTab, setSelectedTab] = useState(0);
  const { t } = useTranslation();

  const handleTabChange = useCallback((selectedTabIndex: number) => {
    setSelectedTab(selectedTabIndex);
  }, []);

  const formatCustomerName = () => {
    const firstName = customer.firstName || "";
    const lastName = customer.lastName || "";
    if (firstName || lastName) {
      return `${firstName} ${lastName}`.trim();
    }
    return customer.email || `Client ${customer.customerId}`;
  };

  const shopifyUrl = `https://admin.shopify.com/store/${shop.split(".")[0]}/customers/${customer.customerId}`;

  // Préparer les données pour les onglets
  const pointsRows = customer.history
    .filter((h: any) => h.action !== "referral")
    .map((history: any) => [
      history.action === "earn" ? t("admin.customers.earned") :
      history.action === "redeem" ? t("admin.customers.redeemed") :
      history.action === "signup" ? t("admin.customers.signup") : history.action,
      `${history.points > 0 ? "+" : ""}${history.points} points`,
      new Date(history.timestamp).toLocaleDateString("fr-FR")
    ]);

  const referralRows = customer.referrals.map((referral: any) => [
    referral.referred ?
      `${referral.referred.firstName || ""} ${referral.referred.lastName || ""}`.trim() ||
      referral.referred.email || "Client anonyme" :
      "En attente",
    <Badge tone={referral.status === "completed" ? t("common.success") : t("common.warning")}>
      {referral.status === "completed" ? "Validé" : "En attente"}
    </Badge>,
    "—", // Total commande (à implémenter)
    new Date(referral.createdAt).toLocaleDateString("fr-FR")
  ]);

  const rewardRows: string | any[] = []; // À implémenter avec le modèle Reward

  const orderRows = customer.orders.map((order: any) => [
    order.orderId,
    `${order.total.toFixed(2)}€`,
    <Badge tone={order.paymentStatus === "paid" ? t("common.success") : t("common.warning") }>
      {order.paymentStatus === "paid" ? t("admin.customers.paid") : order.paymentStatus}
    </Badge>,
    new Date(order.createdAt).toLocaleDateString("fr-FR")
  ]);

  const tabs = [
    {
      id: "points",
      content: t("admin.customers.pointsTab"),
      panelID: "points-panel"
    },
    {
      id: "referrals",
      content: t("admin.customers.referralsTab"),
      panelID: "referrals-panel"
    },
    {
      id: "rewards",
      content: t("admin.customers.rewardsTab"),
      panelID: "rewards-panel"
    }
  ];

  const renderTabContent = () => {
    switch (selectedTab) {
      case 0:
        return (
          <DataTable
            columnContentTypes={["text", "text", "text"]}
            headings={[t("admin.customers.action"), t("admin.customers.points"), t("admin.customers.date")]}
            rows={pointsRows}
          />
        );
      case 1:
        return (
          <DataTable
            columnContentTypes={["text", "text", "text", "text"]}
            headings={[t("admin.customers.referee"), t("admin.customers.referralStatus"), t("admin.customers.referralOrderTotal"), t("admin.customers.date")]}
            rows={referralRows}
          />
        );
      case 2:
        return (
          <DataTable
            columnContentTypes={["text", "text", "text", "text"]}
            headings={[t("admin.customers.reward"), t("admin.customers.code"), t("admin.customers.referralStatus"), t("admin.customers.date")]}
            rows={rewardRows.length > 0 ? rewardRows : [[t("admin.customers.noRewards"), "—", "—", "—"]]}
          />
        );
      default:
        return null;
    }
  };

  return (
    <AdminLayout title={formatCustomerName()}>
      <Layout>
        <Layout.Section>
          <div style={{ marginBottom: "20px", display: "flex", justifyContent: "space-between", alignItems: "center" }}>
            <div style={{ display: "flex", alignItems: "center", gap: "12px" }}>
              <Link to="/app/customers">
                <Button icon={ArrowLeftIcon} variant="tertiary">
                  {t("admin.customers.back")}
                </Button>
              </Link>
              <Text as="h1" variant="headingLg">
                {formatCustomerName()}
              </Text>
            </div>
            <Button
              icon={ViewIcon}
              variant="primary"
              url={shopifyUrl}
              external={true}
              target={"_blank"}
            >
              {t("admin.customers.viewInShopify")}
            </Button>
          </div>
        </Layout.Section>

        <Layout.Section>
          <Grid>
            <Grid.Cell columnSpan={{ xs: 6, sm: 3, md: 4, lg: 8 }}>
              <BlockStack gap="400">
                {/* Informations client */}
                <Card>
                  <BlockStack gap="300">
                    <Text as="h2" variant="headingMd">{t("admin.customers.infoTitle")}</Text>
                    <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
                      <Text as="span" variant="bodyMd">{t("admin.customers.type")}:</Text>
                      <Badge tone={customer.type === "member" ? t("common.success") : "info"}>
                        {customer.type === "member" ? t("admin.customers.member") : t("admin.customers.guest")}
                      </Badge>
                    </div>
                    {customer.email && (
                      <Text as="p" variant="bodyMd">
                        <strong>{t("admin.customers.email")}:</strong> {customer.email}
                      </Text>
                    )}
                    <Text as="p" variant="bodyMd">
                      <strong>{t("admin.customers.joinedOn")}:</strong> {new Date(customer.joinedAt).toLocaleDateString("fr-FR")}
                    </Text>
                  </BlockStack>
                </Card>

                {/* Activité */}
                <Card>
                  <BlockStack gap="400">
                    <Text as="h2" variant="headingMd">{t("admin.customers.activityTitle")}</Text>
                    <Tabs tabs={tabs} selected={selectedTab} onSelect={handleTabChange}>
                      <div style={{ padding: "16px 0" }}>
                        {renderTabContent()}
                      </div>
                    </Tabs>
                  </BlockStack>
                </Card>

                {/* Commandes */}
                <Card>
                  <BlockStack gap="400">
                    <Text as="h2" variant="headingMd">{t("admin.customers.ordersTitle")}</Text>
                    <DataTable
                      columnContentTypes={["text", "text", "text", "text"]}
                      headings={[
                        t("admin.customers.orderId"),
                        t("admin.customers.total"),
                        t("admin.customers.status"),
                        t("admin.customers.date")
                      ]}
                      rows={orderRows.length > 0 ? orderRows : [[t("admin.customers.noOrders"), t("admin.customers.none"), t("admin.customers.none"), t("admin.customers.none")]]}
                    />
                  </BlockStack>
                </Card>
              </BlockStack>
            </Grid.Cell>

            <Grid.Cell columnSpan={{ xs: 6, sm: 3, md: 2, lg: 4 }}>
              <BlockStack gap="400">
                {/* Points */}
                <Card>
                  <BlockStack gap="300">
                    <Text as="h3" variant="headingMd">{t("admin.customers.pointsTab")}</Text>
                    <Text as="p" variant="headingLg" fontWeight="bold">
                      {customer.points} {t("admin.customers.points")}
                    </Text>
                    <Text as="p" variant="bodyMd" tone="subdued">
                      {t("admin.customers.currentBalance")}
                    </Text>
                  </BlockStack>
                </Card>

                {/* Statistiques */}
                <Card>
                  <BlockStack gap="300">
                    <Text as="h3" variant="headingMd">{t("admin.customers.statsTitle")}</Text>
                    <Text as="p" variant="bodyMd">
                      <strong>{t("admin.customers.totalSpent")}:</strong> {customer.totalSpent.toFixed(2)}€
                    </Text>
                    <Text as="p" variant="bodyMd">
                      <strong>{t("admin.customers.ordersCount")}:</strong> {customer.ordersCount}
                    </Text>
                    <Text as="p" variant="bodyMd">
                      <strong>{t("admin.customers.completedReferrals")}:</strong> {customer.referrals.filter((r: any) => r.status === "completed").length}
                    </Text>
                  </BlockStack>
                </Card>

                {/* Parrainage */}
                <Card>
                  <BlockStack gap="300">
                    <Text as="h3" variant="headingMd">{t("admin.customers.referralTitle")}</Text>
                    <Text as="p" variant="bodyMd" tone="subdued">
                      {t("admin.customers.referralFeatureComing")}
                    </Text>
                    <Text as="p" variant="bodyMd" tone="subdued">
                      {t("admin.customers.referralCodeInfo")}
                    </Text>
                  </BlockStack>
                </Card>
              </BlockStack>
            </Grid.Cell>
          </Grid>
        </Layout.Section>
      </Layout>
    </AdminLayout>
  );
}
