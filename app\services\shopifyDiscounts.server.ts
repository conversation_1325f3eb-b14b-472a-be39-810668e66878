import { authenticate } from "../shopify.server";
import { createDiscountWithStoredToken } from "./shopifyAuth.server";

export interface DiscountCode {
  code: string;
  value: number;
  type: "fixed_amount" | "percentage";
  expiresAt?: Date;
  usageLimit?: number;
  minimumAmount?: number;
}

export interface CreateDiscountResult {
  success: boolean;
  code?: string;
  discountId?: string;
  error?: string;
}

/**
 * Générer un code de réduction unique
 */
function generateDiscountCode(): string {
  const prefix = "LOYALTY";
  const randomPart = Math.random().toString(36).substring(2, 8).toUpperCase();
  return `${prefix}${randomPart}`;
}

/**
 * Créer un code de réduction Shopify via l'API GraphQL
 */
export async function createShopifyDiscountCode(
  request: Request,
  discountData: DiscountCode
): Promise<CreateDiscountResult> {
  try {
    console.log("discount data = ", discountData)
    const { admin } = await authenticate.admin(request);
    console.log("admin = ", admin)
    const code = discountData.code || generateDiscountCode();

    // Préparer les données pour la mutation GraphQL
    const discountInput = {
      title: `Loyalty Reward - ${code}`,
      code: code,
      startsAt: new Date().toISOString(),
      endsAt: discountData.expiresAt?.toISOString(),
      usageLimit: discountData.usageLimit || 1,
      appliesOncePerCustomer: true,
      customerSelection: {
        all: true
      }
    };

    let mutation: string;
    let variables: any;

    if (discountData.type === "fixed_amount") {
      // Code de réduction à montant fixe
      mutation = `
        mutation discountCodeBasicCreate($basicCodeDiscount: DiscountCodeBasicInput!) {
          discountCodeBasicCreate(basicCodeDiscount: $basicCodeDiscount) {
            codeDiscountNode {
              id
              codeDiscount {
                ... on DiscountCodeBasic {
                  title
                  codes(first: 1) {
                    nodes {
                      code
                    }
                  }
                  status
                  summary
                }
              }
            }
            userErrors {
              field
              message
            }
          }
        }
      `;

      variables = {
        basicCodeDiscount: {
          ...discountInput,
          customerGets: {
            value: {
              discountAmount: {
                amount: discountData.value.toString(),
                appliesOnEachItem: false
              }
            },
            items: {
              all: true
            }
          },
          minimumRequirement: discountData.minimumAmount ? {
            subtotal: {
              greaterThanOrEqualToSubtotal: discountData.minimumAmount.toString()
            }
          } : undefined
        }
      };
    } else {
      // Code de réduction en pourcentage
      mutation = `
        mutation discountCodeBasicCreate($basicCodeDiscount: DiscountCodeBasicInput!) {
          discountCodeBasicCreate(basicCodeDiscount: $basicCodeDiscount) {
            codeDiscountNode {
              id
              codeDiscount {
                ... on DiscountCodeBasic {
                  title
                  codes(first: 1) {
                    nodes {
                      code
                    }
                  }
                  status
                  summary
                }
              }
            }
            userErrors {
              field
              message
            }
          }
        }
      `;

      variables = {
        basicCodeDiscount: {
          ...discountInput,
          customerGets: {
            value: {
              percentage: discountData.value / 100 // Convertir en décimal
            },
            items: {
              all: true
            }
          },
          minimumRequirement: discountData.minimumAmount ? {
            subtotal: {
              greaterThanOrEqualToSubtotal: discountData.minimumAmount.toString()
            }
          } : undefined
        }
      };
    }
    console.log("starting the graphql mutation ")
    const response = await admin.graphql(mutation, { variables });
    const data = await response.json();
    console.log("data", data)
    if (data.errors) {
      console.error("GraphQL errors:", data.errors);
      return {
        success: false,
        error: "Erreur GraphQL lors de la création du code de réduction"
      };
    }

    const result = data.data.discountCodeBasicCreate;

    if (result.userErrors && result.userErrors.length > 0) {
      console.error("User errors:", result.userErrors);
      return {
        success: false,
        error: result.userErrors[0].message
      };
    }

    const createdCode = result.codeDiscountNode?.codeDiscount?.codes?.nodes?.[0]?.code;
    const discountId = result.codeDiscountNode?.id;

    if (!createdCode) {
      return {
        success: false,
        error: "Code de réduction non créé"
      };
    }

    return {
      success: true,
      code: createdCode,
      discountId: discountId
    };

  } catch (error) {
    console.error("Error creating Shopify discount code:", error);
    return {
      success: false,
      error: "Erreur lors de la création du code de réduction"
    };
  }
}

/**
 * Créer un code de réduction basé sur les points échangés
 * Version avec authentification par token stocké (pour les appels depuis le widget)
 */
export async function createLoyaltyDiscountCode(
  shop: string,
  pointsSpent: number,
  redemptionRate: number,
  expiryDays: number = 30,
  customerId?: string
): Promise<CreateDiscountResult> {
  // Calculer la valeur en euros : points / taux de conversion
  const discountValue = Math.round((pointsSpent / redemptionRate) * 100) / 100;

  const expiresAt = new Date();
  expiresAt.setDate(expiresAt.getDate() + expiryDays);

  const code = generateDiscountCode();

  return await createDiscountWithStoredToken(shop, {
    title: `Loyalty Reward - ${code}`,
    code: code,
    value: discountValue,
    type: "fixed_amount",
    expiresAt: expiresAt,
    usageLimit: 1,
    customerId: customerId
  });
}

/**
 * Créer un code de réduction basé sur les points échangés
 * Version avec authentification par request (pour les appels depuis l'admin)
 */
export async function createLoyaltyDiscountCodeWithRequest(
  request: Request,
  pointsSpent: number,
  redemptionRate: number,
  expiryDays: number = 30
): Promise<CreateDiscountResult> {
  // Calculer la valeur en euros : points / taux de conversion
  const discountValue = Math.round((pointsSpent / redemptionRate) * 100) / 100;

  const expiresAt = new Date();
  expiresAt.setDate(expiresAt.getDate() + expiryDays);

  const discountData: DiscountCode = {
    code: generateDiscountCode(),
    value: discountValue,
    type: "fixed_amount",
    expiresAt: expiresAt,
    usageLimit: 1
  };

  return await createShopifyDiscountCode(request, discountData);
}
