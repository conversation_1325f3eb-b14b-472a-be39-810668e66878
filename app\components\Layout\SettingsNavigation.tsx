import { Navigation } from '@shopify/polaris';
import { useLocation, useNavigate } from '@remix-run/react';

export function SettingsNavigation() {
  const location = useLocation();
  const navigate = useNavigate();

  const navigationItems = [
    {
      label: 'Paramètres généraux',
      url: '/app/settings',
      icon: 'SettingsIcon',
      selected: location.pathname === '/app/settings'
    },
    {
      label: 'Personnaliser le widget',
      url: '/app/settings/widget',
      icon: 'PaintBrushIcon', 
      selected: location.pathname === '/app/settings/widget'
    },
    {
      label: 'Produits échangeables',
      url: '/app/settings/products',
      icon: 'ProductIcon',
      selected: location.pathname === '/app/settings/products'
    }
  ];

  return (
    <Navigation location={location.pathname}>
      <Navigation.Section
        items={navigationItems.map(item => ({
          label: item.label,
          url: item.url,
          selected: item.selected,
          onClick: () => navigate(item.url)
        }))}
      />
    </Navigation>
  );
}
