import prisma from "../db.server";

export interface WayToRedeem {
  id: string;
  shop: string;
  name: string;
  description: string;
  redeemType: "discount" | "product" | "shipping" | "coupon";
  redeemValue: number;
  pointsCost: number;
  icon: string;
  isActive: boolean;
  isConfigurable: boolean;
  minPoints?: number;
  maxPoints?: number;
  minValue?: number;
  maxValue?: number;
  expiryDays: number;
  usageLimit: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateWayToRedeemData {
  name: string;
  description: string;
  redeemType: "discount" | "product" | "shipping" | "coupon";
  redeemValue: number;
  pointsCost: number;
  icon?: string;
  isActive?: boolean;
  isConfigurable?: boolean;
  minPoints?: number;
  maxPoints?: number;
  minValue?: number;
  maxValue?: number;
  expiryDays?: number;
  usageLimit?: number;
}

export interface UpdateWayToRedeemData extends Partial<CreateWayToRedeemData> {}

export async function getWaysToRedeem(shop: string) {
  try {
    if (!prisma) {
      console.error("Prisma client not initialized");
      return [];
    }

    return await prisma.wayToRedeem.findMany({
      where: { shop },
      orderBy: { createdAt: "desc" }
    });
  } catch (error) {
    console.error("Error fetching ways to redeem:", error);
    return [];
  }
}

export async function getWayToRedeemById(id: string, shop: string) {
  try {
    if (!prisma) {
      console.error("Prisma client not initialized");
      return null;
    }

    return await prisma.wayToRedeem.findFirst({
      where: { id, shop }
    });
  } catch (error) {
    console.error("Error fetching way to redeem:", error);
    return null;
  }
}

export async function createWayToRedeem(shop: string, data: CreateWayToRedeemData) {
  try {
    if (!prisma) {
      console.error("Prisma client not initialized");
      return null;
    }

    return await prisma.wayToRedeem.create({
      data: {
        shop,
        name: data.name,
        description: data.description,
        redeemType: data.redeemType,
        redeemValue: data.redeemValue,
        pointsCost: data.pointsCost,
        icon: data.icon || "discount",
        isActive: data.isActive ?? true,
        isConfigurable: data.isConfigurable ?? false,
        minPoints: data.minPoints,
        maxPoints: data.maxPoints,
        minValue: data.minValue,
        maxValue: data.maxValue,
        expiryDays: data.expiryDays ?? 30,
        usageLimit: data.usageLimit ?? 1
      }
    });
  } catch (error) {
    console.error("Error creating way to redeem:", error);
    return null;
  }
}

export async function updateWayToRedeem(id: string, shop: string, data: UpdateWayToRedeemData) {
  try {
    if (!prisma) {
      console.error("Prisma client not initialized");
      return null;
    }

    return await prisma.wayToRedeem.update({
      where: { id },
      data: {
        ...data,
        updatedAt: new Date()
      }
    });
  } catch (error) {
    console.error("Error updating way to redeem:", error);
    return null;
  }
}

export async function deleteWayToRedeem(id: string, shop: string) {
  try {
    if (!prisma) {
      console.error("Prisma client not initialized");
      return false;
    }

    await prisma.wayToRedeem.delete({
      where: { id }
    });
    return true;
  } catch (error) {
    console.error("Error deleting way to redeem:", error);
    return false;
  }
}

export async function initializeDefaultWayToRedeem(shop: string) {
  try {
    const existingWays = await getWaysToRedeem(shop);
    if (existingWays.length > 0) {
      return existingWays;
    }

    // Créer les ways to redeem par défaut
    const defaultWays = await Promise.all([
      // Coupon configurable
      createWayToRedeem(shop, {
        name: "Coupon de réduction",
        description: "Échangez vos points contre un coupon de réduction",
        redeemType: "coupon",
        redeemValue: 0, // Valeur configurable
        pointsCost: 0, // Coût configurable
        icon: "discount",
        isActive: true,
        isConfigurable: true,
        minPoints: 100,
        maxPoints: 10000,
        minValue: 1,
        maxValue: 100,
        expiryDays: 30,
        usageLimit: 1
      }),

      // Coupon fixe 5€
      createWayToRedeem(shop, {
        name: "Coupon 5€",
        description: "Coupon de réduction de 5€",
        redeemType: "coupon",
        redeemValue: 5,
        pointsCost: 500,
        icon: "discount",
        isActive: true,
        isConfigurable: false,
        expiryDays: 30,
        usageLimit: 1
      })
    ]);

    return defaultWays.filter(way => way !== null);
  } catch (error) {
    console.error("Error initializing default way to redeem:", error);
    return [];
  }
}
