import { json, type LoaderFunctionArgs, type ActionFunctionArgs } from "@remix-run/node";
import { authenticate } from "../shopify.server";
import { 
  getExchangeableProducts, 
  saveExchangeableProducts,
  addExchangeableProduct,
  removeExchangeableProduct,
  updateProductPointsCost
} from "../models/ExchangeableProducts.server";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  try {
    const { session } = await authenticate.admin(request);
    const shop = session.shop;

    const products = await getExchangeableProducts(shop);
    return json({ products });
  } catch (error) {
    console.error("Error loading exchangeable products:", error);
    return json({ products: [], error: "Erreur lors du chargement" }, { status: 500 });
  }
};

export const action = async ({ request }: ActionFunctionArgs) => {
  try {
    const { session } = await authenticate.admin(request);
    const shop = session.shop;
    
    const formData = await request.formData();
    const actionType = formData.get("actionType") as string;

    switch (actionType) {
      case "save": {
        const productsJson = formData.get("products") as string;
        const products = JSON.parse(productsJson);
        
        const success = await saveExchangeableProducts(shop, products);
        if (success) {
          return json({ success: true, message: "Produits sauvegardés avec succès" });
        } else {
          return json({ success: false, error: "Erreur lors de la sauvegarde" }, { status: 500 });
        }
      }

      case "add": {
        const productData = {
          shopifyProductId: formData.get("shopifyProductId") as string,
          title: formData.get("title") as string,
          handle: formData.get("handle") as string,
          image: formData.get("image") as string,
          price: formData.get("price") as string,
          pointsCost: parseInt(formData.get("pointsCost") as string) || 0
        };

        const success = await addExchangeableProduct(shop, productData);
        if (success) {
          return json({ success: true, message: "Produit ajouté avec succès" });
        } else {
          return json({ success: false, error: "Produit déjà ajouté ou erreur" }, { status: 400 });
        }
      }

      case "remove": {
        const productId = formData.get("productId") as string;
        
        const success = await removeExchangeableProduct(shop, productId);
        if (success) {
          return json({ success: true, message: "Produit supprimé avec succès" });
        } else {
          return json({ success: false, error: "Erreur lors de la suppression" }, { status: 500 });
        }
      }

      case "updateCost": {
        const productId = formData.get("productId") as string;
        const pointsCost = parseInt(formData.get("pointsCost") as string) || 0;
        
        const success = await updateProductPointsCost(shop, productId, pointsCost);
        if (success) {
          return json({ success: true, message: "Coût mis à jour avec succès" });
        } else {
          return json({ success: false, error: "Erreur lors de la mise à jour" }, { status: 500 });
        }
      }

      default:
        return json({ success: false, error: "Action non reconnue" }, { status: 400 });
    }
  } catch (error) {
    console.error("Error in exchangeable products API:", error);
    return json({ success: false, error: "Erreur serveur" }, { status: 500 });
  }
};
