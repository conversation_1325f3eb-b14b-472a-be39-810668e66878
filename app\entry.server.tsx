import { PassThrough } from "stream";
import { renderToPipeableStream } from "react-dom/server";
import { RemixServer } from "@remix-run/react";
import {
  createReadableStreamFromReadable,
  type EntryContext,
} from "@remix-run/node";
import { isbot } from "isbot";
import { addDocumentResponseHeaders } from "./shopify.server";

export const streamTimeout = 10000;

interface ExtendedEntryContext extends EntryContext {
  loaderData?: Record<string, unknown>;
  actionData?: Record<string, unknown>;
  errors?: Record<string, unknown>;
}

export default async function handleRequest(
  request: Request,
  responseStatusCode: number,
  responseHeaders: Headers,
  remixContext: EntryContext
) {
  addDocumentResponseHeaders(request, responseHeaders);
  const userAgent = request.headers.get("user-agent");
  const callbackName = isbot(userAgent ?? '')
    ? "onAllReady"
    : "onShellReady";

  const extendedContext = remixContext as ExtendedEntryContext;

  return new Promise((resolve, reject) => {
    let didError = false;

    const { pipe, abort } = renderToPipeableStream(
      <RemixServer
        context={remixContext}
        url={request.url}
        abortDelay={streamTimeout}
      />,
      {
        [callbackName]: () => {
          const body = new PassThrough();
          const stream = createReadableStreamFromReadable(body);

          responseHeaders.set("Content-Type", "text/html");
          responseHeaders.set("Cache-Control", "no-store, no-cache");

          resolve(
            new Response(stream, {
              headers: responseHeaders,
              status: didError ? 500 : responseStatusCode,
            })
          );
          pipe(body);
        },
        onShellError(error) {
          didError = true;
          console.error('Shell error:', error);
          reject(error);
        },
        onError(error) {
          didError = true;
          console.error('Rendering error:', error);
          responseStatusCode = 500;
        },
        bootstrapScriptContent: `window.__remixContext = ${JSON.stringify({
          ...remixContext,
          state: {
            loaderData: extendedContext.loaderData || {},
            actionData: extendedContext.actionData,
            errors: extendedContext.errors,
          },
        })};`,
      }
    );

    setTimeout(abort, streamTimeout);
  });
}
