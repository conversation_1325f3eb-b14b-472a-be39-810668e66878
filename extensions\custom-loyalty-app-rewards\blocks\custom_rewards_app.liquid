<!-- Chargement des assets CSS et JS -->
{{ 'loyalty-widget.css' | asset_url | stylesheet_tag }}

<!-- Script de traduction (chargé en premier) -->
<script src="{{ 'loyalty-translation.js' | asset_url }}" defer></script>

<!-- Script de capture des codes de parrainage (chargé avec defer) -->
<script src="{{ 'referral-capture.js' | asset_url }}" defer></script>

<!-- Widget de fidélité flottant -->
<div id="loyalty-widget" class="loyalty-widget-container"
     data-shop="{{ shop.domain }}"
     data-customer-id="{% if customer %}{{ customer.id }}{% endif %}"
     data-position="{{ block.settings.position }}"
     data-primary-color="{{ block.settings.primary_color }}"
     data-secondary-color="{{ block.settings.secondary_color }}"
     data-show-on-mobile="{{ block.settings.show_on_mobile }}"
     data-auto-open="{{ block.settings.auto_open }}">
  <!-- Bouton flottant principal -->
  <div id="loyalty-trigger" class="loyalty-trigger">
    <div class="loyalty-trigger-content">
      <div class="loyalty-icon">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M12 2L15.09 8.26L22 9L17 14L18.18 21L12 17.77L5.82 21L7 14L2 9L8.91 8.26L12 2Z" fill="currentColor"/>
        </svg>
      </div>
      <div class="loyalty-points-preview" id="points-preview">
        <span class="points-count">0</span>
        <span class="points-label" data-translate="widget.pointsLabel">pts</span>
      </div>
    </div>
    <div class="loyalty-pulse"></div>
  </div>

  <!-- Panel principal du widget -->
  <div id="loyalty-panel" class="loyalty-panel">
    <div class="loyalty-panel-header">
      <div class="loyalty-header-content">
        <h3 class="loyalty-title" id="loyalty-program-title" data-translate="widget.title">Programme de Fidélité</h3>
        <button id="loyalty-close" class="loyalty-close-btn">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
          </svg>
        </button>
      </div>
    </div>

    <div class="loyalty-panel-body">
      <!-- État de chargement -->
      <div id="loyalty-loading" class="loyalty-loading">
        <div class="loyalty-spinner"></div>
        <p data-translate="widget.loading">Chargement de vos points...</p>
      </div>

      <!-- État non connecté -->
      <div id="loyalty-guest" class="loyalty-guest " style="display: none;">
        <div class="loyalty-guest-icon">
          <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
            <path d="M8 14s1.5 2 4 2 4-2 4-2" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
            <path d="M9 9h.01M15 9h.01" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
          </svg>
        </div>
        <h4 id="loyalty-program-name" data-translate="widget.guest.title">Rejoignez notre programme !</h4>
        <p id="loyalty-program-description" data-translate="widget.guest.description">Gagnez des points à chaque achat et débloquez des récompenses exclusives.</p>
        {% if customer %}
          <button class="loyalty-btn loyalty-btn-primary" onclick="loyaltyWidget.signupToLoyalty()" data-translate="widget.guest.joinButton">
            Rejoindre le programme
          </button>
        {% else %}
          <a href="/account/login" class="loyalty-btn loyalty-btn-primary" data-translate="widget.guest.loginButton">
            Se connecter
          </a>
        {% endif %}
      </div>

      <!-- État connecté -->
      <div id="loyalty-member" class="loyalty-member" style="display: none;">
        <!-- Informations du client - uniquement visibles sur la page principale -->
        <div class="loyalty-member-header" id="loyalty-member-header">
          <div class="loyalty-customer-info">
            <div class="loyalty-avatar">
              <span id="customer-initials">?</span>
            </div>
            <div class="loyalty-customer-details">
              <h4 id="customer-name" data-translate="widget.member.defaultName">Client</h4>
              <span id="customer-status" class="loyalty-status-badge" data-translate="widget.member.statusGuest">Invité</span>
            </div>
          </div>

          <!-- Points et statistiques -->
          <div class="loyalty-stats">
            <div class="loyalty-stat-card loyalty-stat-primary">
              <div class="loyalty-stat-icon">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M12 2L15.09 8.26L22 9L17 14L18.18 21L12 17.77L5.82 21L7 14L2 9L8.91 8.26L12 2Z" fill="currentColor"/>
                </svg>
              </div>
              <div class="loyalty-stat-content">
                <span class="loyalty-stat-value" id="customer-points">0</span>
                <span class="loyalty-stat-label" data-translate="widget.member.pointsLabel">Points</span>
              </div>
            </div>

            <div class="loyalty-stat-card">
              <div class="loyalty-stat-icon">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M16 11V7a4 4 0 0 0-8 0v4H6a2 2 0 0 0-2 2v7a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-7a2 2 0 0 0-2-2h-2z" stroke="currentColor" stroke-width="2"/>
                </svg>
              </div>
              <div class="loyalty-stat-content">
                <span class="loyalty-stat-value" id="customer-orders">0</span>
                <span class="loyalty-stat-label" data-translate="widget.member.ordersLabel">Commandes</span>
              </div>
            </div>
          </div>

          <!-- Progression vers la prochaine récompense -->
          {% comment %} <div class="loyalty-progress">
            <div class="loyalty-progress-header">
              <span class="loyalty-progress-title" data-translate="widget.member.nextReward">Prochaine récompense</span>
              <span class="loyalty-progress-remaining" id="points-needed">500 <span data-translate="widget.member.pointsUnit">points</span></span>
            </div>
            <div class="loyalty-progress-bar">
              <div class="loyalty-progress-fill" id="progress-fill" style="width: 0%"></div>
            </div>
            <p class="loyalty-progress-text" data-translate="widget.member.progressMessage">Continuez vos achats pour débloquer votre prochaine récompense !</p>
          </div> {% endcomment %}
        </div>

        <!-- Section principale - Navigation par cartes -->
        <div class="loyalty-main-section" id="loyalty-main-section">
          <!-- Section Récompenses disponibles -->
          <div class="loyalty-nav-card" id="your-rewards-card" onclick="loyaltyWidget.showYourRewardsSection()">
            <div class="loyalty-nav-card-content">
              <div class="loyalty-nav-card-title" data-translate="widget.navigation.yourRewards">Your rewards</div>
              <div class="loyalty-nav-card-subtitle" id="rewards-count" data-translate="widget.rewards.countZero">You have 0 reward available</div>
            </div>
            <div class="loyalty-nav-card-arrow">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <polyline points="9,18 15,12 9,6"></polyline>
              </svg>
            </div>
          </div>

          <!-- Section Ways to Earn -->
          <div class="loyalty-nav-card" id="ways-to-earn-card" onclick="loyaltyWidget.showWaysToEarnSection()">
            <div class="loyalty-nav-card-icon">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <circle cx="12" cy="12" r="10"></circle>
                <path d="M12 6v6l4 2"></path>
              </svg>
            </div>
            <div class="loyalty-nav-card-content">
              <div class="loyalty-nav-card-title" data-translate="widget.navigation.waysToEarn">Ways to earn</div>
            </div>
            <div class="loyalty-nav-card-arrow">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <polyline points="9,18 15,12 9,6"></polyline>
              </svg>
            </div>
          </div>

          <!-- Section Ways to Redeem -->
          <div class="loyalty-nav-card" id="ways-to-redeem-card" onclick="loyaltyWidget.showWaysToRedeemSection()">
            <div class="loyalty-nav-card-icon">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <circle cx="12" cy="12" r="10"></circle>
                <path d="M16 12l-4-4-4 4"></path>
                <path d="M12 16V8"></path>
              </svg>
            </div>
            <div class="loyalty-nav-card-content">
              <div class="loyalty-nav-card-title" data-translate="widget.navigation.waysToRedeem">Ways to redeem</div>
              <div class="loyalty-nav-card-subtitle" data-translate="widget.navigation.redeemDescription">Exchange your points for rewards</div>
            </div>
            <div class="loyalty-nav-card-arrow">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <polyline points="9,18 15,12 9,6"></polyline>
              </svg>
            </div>
          </div>

          <!-- Section Parrainage -->
          <div class="loyalty-referral-section" id="loyalty-referral-section">
            <div class="loyalty-referral-header">
              <h6 data-translate="widget.navigation.referFriends">Refer your friends</h6>
              <span class="loyalty-referral-count" id="referral-count" data-translate="widget.referral.completed" data-translate-params='{"count": 0}'>0 referrals completed</span>
            </div>
            <p class="loyalty-referral-description" id="referral-description" data-translate="widget.referral.description">
              Share this link with your friends to earn rewards when they join!
            </p>
            <div class="loyalty-referral-link-container">
              <input type="text" class="loyalty-referral-input" id="referral-link" readonly
                     value="https://refs.cc/NnoKl5oL?smile_ref" />
              <button class="loyalty-copy-btn" id="copy-referral-link">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                  <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                </svg>
              </button>
            </div>
            <div class="loyalty-social-share">
              <button class="loyalty-social-btn loyalty-facebook-btn" id="share-facebook">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                </svg>
                <span data-translate="widget.referral.facebook">Facebook</span>
              </button>
              <button class="loyalty-social-btn loyalty-twitter-btn" id="share-twitter">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                </svg>
                <span data-translate="widget.referral.twitter">X</span>
              </button>
              <button class="loyalty-social-btn loyalty-email-btn" id="share-email">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
                  <polyline points="22,6 12,13 2,6"></polyline>
                </svg>
                <span data-translate="widget.referral.email">Email</span>
              </button>
            </div>
          </div>

          <!-- Section Your Activity -->
          <div class="loyalty-nav-card" id="your-activity-card" onclick="loyaltyWidget.showYourActivitySection()">
            <div class="loyalty-nav-card-icon">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <circle cx="12" cy="12" r="3"></circle>
                <path d="M19.4 15a1.65 1.65 0 00.33 1.82l.06.06a2 2 0 010 2.83 2 2 0 01-2.83 0l-.06-.06a1.65 1.65 0 00-1.82-.33 1.65 1.65 0 00-1 1.51V21a2 2 0 01-2 2 2 2 0 01-2-2v-.09A1.65 1.65 0 009 19.4a1.65 1.65 0 00-1.82.33l-.06.06a2 2 0 01-2.83 0 2 2 0 010-2.83l.06-.06a1.65 1.65 0 00.33-1.82 1.65 1.65 0 00-1.51-1H3a2 2 0 01-2-2 2 2 0 012-2h.09A1.65 1.65 0 004.6 9a1.65 1.65 0 00-.33-1.82l-.06-.06a2 2 0 010-2.83 2 2 0 012.83 0l.06.06a1.65 1.65 0 001.82.33H9a1.65 1.65 0 001-1.51V3a2 2 0 012-2 2 2 0 012 2v.09a1.65 1.65 0 001 1.51 1.65 1.65 0 001.82-.33l.06-.06a2 2 0 012.83 0 2 2 0 010 2.83l-.06.06a1.65 1.65 0 00-.33 1.82V9a1.65 1.65 0 001.51 1H21a2 2 0 012 2 2 2 0 01-2 2h-.09a1.65 1.65 0 00-1.51 1z"></path>
              </svg>
            </div>
            <div class="loyalty-nav-card-content">
              <div class="loyalty-nav-card-title" data-translate="widget.navigation.yourActivity">Your activity</div>
            </div>
            <div class="loyalty-nav-card-arrow">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <polyline points="9,18 15,12 9,6"></polyline>
              </svg>
            </div>
          </div>
        </div>

        <!-- Section Your Rewards -->
        <div class="loyalty-your-rewards-section" id="loyalty-your-rewards-section" style="display: none;">
          <div class="loyalty-section-header">
            <button class="loyalty-back-btn" id="back-from-your-rewards">
              <span class="loyalty-back-icon">←</span>
              <span id="customer-points-header-rewards">8,700 Points</span>
            </button>
          </div>
          <div class="loyalty-section-content">
            <h5 data-translate="widget.rewards.title">Your rewards</h5>
            <div class="loyalty-your-rewards-list" id="your-rewards-list">
              <!-- Sera rempli dynamiquement -->
            </div>
            <div class="loyalty-past-rewards">
              <h6 data-translate="widget.rewards.pastRewards">Past rewards</h6>
              <div class="loyalty-past-rewards-list" id="past-rewards-list">
                <!-- Sera rempli dynamiquement -->
              </div>
            </div>
          </div>
        </div>

        <!-- Section Ways to Earn -->
        <div class="loyalty-ways-to-earn-section" id="loyalty-ways-to-earn-section" style="display: none;">
          <div class="loyalty-section-header">
            <button class="loyalty-back-btn" id="back-from-ways-to-earn">
              <span class="loyalty-back-icon">←</span>
              <span id="customer-points-header-earn">8,700 Points</span>
            </button>
          </div>
          <div class="loyalty-section-content">
            <h5 data-translate="widget.earn.title">Ways to earn</h5>
            <div class="loyalty-ways-to-earn-list" id="ways-to-earn-list">
              <!-- Sera rempli dynamiquement -->
            </div>
          </div>
        </div>

        <!-- Section Ways to Redeem -->
        <div class="loyalty-ways-to-redeem-section" id="loyalty-ways-to-redeem-section" style="display: none;">
          <div class="loyalty-section-header">
            <button class="loyalty-back-btn" id="back-from-ways-to-redeem">
              <span class="loyalty-back-icon">←</span>
              <span id="customer-points-header-redeem">8,700 Points</span>
            </button>
          </div>
          <div class="loyalty-section-content">
            <h5 data-translate="widget.redeem.title">Ways to redeem</h5>
            <div class="loyalty-ways-to-redeem-list" id="ways-to-redeem-list">
              <!-- Sera rempli dynamiquement -->
            </div>
          </div>
        </div>

        <!-- Section Ways to Redeem -->
        <div class="loyalty-rewards-section" id="loyalty-rewards-section" style="display: none;">
          <div class="loyalty-section-header">
            <button class="loyalty-back-btn" id="back-from-rewards">
              <span class="loyalty-back-icon">←</span>
              Retour
            </button>
            <h5>Ways to redeem</h5>
          </div>
          <div class="loyalty-rewards-list" id="rewards-list">
            <!-- Sera rempli dynamiquement -->
          </div>
        </div>

        <!-- Section Configuration du coupon -->
        <div class="loyalty-coupon-config-section" id="loyalty-coupon-config-section" style="display: none;">
          <div class="loyalty-section-header">
            <button class="loyalty-back-btn" id="back-from-coupon-config">
              <span class="loyalty-back-icon">←</span>
              <span id="customer-points-header">6,800 Points</span>
            </button>
          </div>
          <div class="loyalty-coupon-config">
            <div class="loyalty-coupon-preview">
              <div class="loyalty-coupon-icon">💰</div>
              <h6 id="coupon-config-title">€11 off coupon</h6>
              <p id="coupon-config-subtitle">Spent 1,100 Points</p>
            </div>
            <div class="loyalty-coupon-form">
              <label for="points-input" data-translate="widget.coupon.pointsToExchange">Nombre de points à échanger :</label>
              <input type="number" id="points-input" class="loyalty-points-input" min="100" step="100" />
              <div class="loyalty-coupon-value">
                <span data-translate="widget.coupon.couponValue">Valeur du coupon : </span>
                <strong id="coupon-value-display">€0</strong>
              </div>
              <button class="loyalty-btn loyalty-btn-primary" id="redeem-coupon-btn" data-translate="widget.coupon.redeem">
                Redeem
              </button>
            </div>
          </div>
        </div>

        <!-- Section Coupon généré -->
        <div class="loyalty-coupon-result-section" id="loyalty-coupon-result-section" style="display: none;">
          <div class="loyalty-section-header">
            <button class="loyalty-back-btn" id="back-from-coupon-result">
              <span class="loyalty-back-icon">←</span>
              <span id="customer-points-result">8,700 Points</span>
            </button>
          </div>
          <div class="loyalty-coupon-result">
            <div class="loyalty-coupon-generated">
              <div class="loyalty-coupon-icon">💰</div>
              <h6 id="generated-coupon-title">€11 off coupon</h6>
              <p id="generated-coupon-subtitle">Spent 1,100 Points</p>
              <p class="loyalty-coupon-description" data-translate="widget.coupon.instructions">Use this discount code on your next order!</p>
              <div class="loyalty-coupon-code-container">
                <input type="text" id="generated-coupon-code" class="loyalty-coupon-code" readonly />
                <button class="loyalty-copy-btn" id="copy-coupon-code">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                    <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                  </svg>
                </button>
              </div>
              <button class="loyalty-btn loyalty-btn-primary" id="apply-coupon-btn" data-translate="widget.coupon.applyCode">
                Apply code
              </button>
            </div>
          </div>
        </div>

        <!-- Section Your Activity -->
        <div class="loyalty-your-activity-section" id="loyalty-your-activity-section" style="display: none;">
          <div class="loyalty-section-header">
            <button class="loyalty-back-btn" id="back-from-your-activity">
              <span class="loyalty-back-icon">←</span>
              <span id="customer-points-header-activity">8,700 Points</span>
            </button>
          </div>
          <div class="loyalty-section-content">
            <h5 data-translate="widget.activity.title">Activity</h5>
            <div class="loyalty-activity-tabs">
              <button class="loyalty-tab-btn active" id="points-tab" data-translate="widget.activity.pointsTab">Points</button>
              <button class="loyalty-tab-btn" id="referrals-tab" data-translate="widget.activity.referralsTab">Referrals</button>
            </div>
            <div class="loyalty-activity-notice">
              <p data-translate="widget.activity.notice">Your points balance may not reflect your latest activity</p>
            </div>
            <div class="loyalty-activity-list" id="activity-list">
              <!-- Sera rempli dynamiquement -->
            </div>
          </div>
        </div>

        <!-- Section Historique (ancienne) -->
        <div class="loyalty-history-section" id="loyalty-history-section" style="display: none;">
          <div class="loyalty-section-header">
            <button class="loyalty-back-btn" id="back-from-history">
              <span class="loyalty-back-icon">←</span>
              Retour
            </button>
            <h5>📋 Historique des points</h5>
          </div>
          <div class="loyalty-history-list" id="history-list">
            <!-- Sera rempli dynamiquement -->
          </div>
        </div>
      </div>

      <!-- État d'erreur -->
      <div id="loyalty-error" class="loyalty-error" style="display: none;">
        <div class="loyalty-error-icon">
          <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
            <line x1="15" y1="9" x2="9" y2="15" stroke="currentColor" stroke-width="2"/>
            <line x1="9" y1="9" x2="15" y2="15" stroke="currentColor" stroke-width="2"/>
          </svg>
        </div>
        <h4 data-translate="widget.error.title">Oups ! Une erreur est survenue</h4>
        <p data-translate="widget.error.message">Impossible de charger vos informations de fidélité.</p>
        <button class="loyalty-btn loyalty-btn-outline" onclick="loyaltyWidget.reload()" data-translate="widget.actions.retry">
          Réessayer
        </button>
      </div>
    </div>
  </div>

  <!-- Overlay -->
  <div id="loyalty-overlay" class="loyalty-overlay"></div>
</div>

<!-- Chargement du JavaScript -->
<script>
  // Configuration des URLs de traduction
  window.loyaltyTranslationUrls = {
    fr: {{ 'fr.json' | asset_url | json }},
    en: {{ 'en.json' | asset_url | json }},
    de: {{ 'de.json' | asset_url | json }},
    es: {{ 'es.json' | asset_url | json }},
    it: {{ 'it.json' | asset_url | json }},
    nl: {{ 'nl.json' | asset_url | json }},
    pt: {{ 'pt.json' | asset_url | json }},
    pl: {{ 'pl.json' | asset_url | json }}
  };

  // Configuration du widget depuis Liquid
  window.loyaltyWidgetConfig = {
    shop: {{ shop.domain | json }},
    customerId: {% if customer %}{{ customer.id | json }}{% else %}null{% endif %},
    customerEmail: {% if customer %}{{ customer.email | json }}{% else %}null{% endif %},
    customerFirstName: {% if customer %}{{ customer.first_name | json }}{% else %}null{% endif %},
    customerLastName: {% if customer %}{{ customer.last_name | json }}{% else %}null{% endif %},
    position: {{ block.settings.position | json }},
    primaryColor: {{ block.settings.primary_color | json }},
    secondaryColor: {{ block.settings.secondary_color | json }},
    showOnMobile: {{ block.settings.show_on_mobile | json }},
    autoOpen: {{ block.settings.auto_open | json }},
    countryCode: {{ localization.country.iso_code | json }},
    // Pas besoin d'apiUrl, on utilise l'App Proxy
  };
</script>

<script src="{{ 'loyalty-widget.js' | asset_url }}" defer></script>

<!-- Script d'application des traductions -->
<script>
  // Fonction pour appliquer les traductions aux éléments avec data-translate
  function applyTranslations() {
    if (!window.LoyaltyTranslation) {
      // Si le système de traduction n'est pas encore chargé, réessayer dans 100ms
      setTimeout(applyTranslations, 100);
      return;
    }

    const elementsToTranslate = document.querySelectorAll('[data-translate]');
    elementsToTranslate.forEach(element => {
      const key = element.getAttribute('data-translate');
      const paramsAttr = element.getAttribute('data-translate-params');
      let params = {};

      if (paramsAttr) {
        try {
          params = JSON.parse(paramsAttr);
        } catch (e) {
          console.warn('Invalid JSON in data-translate-params:', paramsAttr);
        }
      }

      const translation = window.LoyaltyTranslation.t(key, params);
      if (translation && translation !== key) {
        element.textContent = translation;
      }
    });
  }

  // Appliquer les traductions une fois que tout est chargé
  document.addEventListener('DOMContentLoaded', function() {
    // Attendre que le système de traduction soit initialisé
    setTimeout(applyTranslations, 500);
  });

  // Réappliquer les traductions si le contenu change (pour le contenu dynamique)
  window.addEventListener('loyaltyTranslationsReady', applyTranslations);
</script>

{% schema %}
{
  "name": "Custom Rewards App",
  "target": "body",
  "settings": [
    {
      "type": "header",
      "content": "Configuration du widget"
    },
    {
      "type": "select",
      "id": "position",
      "label": "Position du widget",
      "options": [
        {
          "value": "bottom-right",
          "label": "Bas droite"
        },
        {
          "value": "bottom-left",
          "label": "Bas gauche"
        },
        {
          "value": "top-right",
          "label": "Haut droite"
        },
        {
          "value": "top-left",
          "label": "Haut gauche"
        }
      ],
      "default": "bottom-right"
    },
    {
      "type": "color",
      "id": "primary_color",
      "label": "Couleur principale",
      "default": "#2E7D32"
    },
    {
      "type": "color",
      "id": "secondary_color",
      "label": "Couleur secondaire",
      "default": "#4CAF50"
    },
    {
      "type": "checkbox",
      "id": "show_on_mobile",
      "label": "Afficher sur mobile",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "auto_open",
      "label": "Ouverture automatique pour nouveaux visiteurs",
      "default": false
    }
  ]
}
{% endschema %}
