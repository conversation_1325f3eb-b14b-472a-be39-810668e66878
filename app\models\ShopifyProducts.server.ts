import { authenticate } from "../shopify.server";
import prisma from "../db.server";

export interface ShopifyProduct {
  id: string;
  title: string;
  handle: string;
  image?: string;
  price: string;
  pointsCost?: number;
  variants: {
    id: string;
    price: string;
    title: string;
  }[];
}

/**
 * Récupérer le taux de conversion points/euro depuis la base de données
 */
async function getPointsConversionRate(shop: string): Promise<number> {
  try {
    const settings = await prisma.settings.findUnique({
      where: { shop },
      select: { earningRate: true }
    });

    // redemptionRate = euro par point, donc points par euro = 1 / redemptionRate
    const earningRate = settings?.earningRate || 1; // Par défaut 0.01€ par point
    return Math.round(earningRate); // Conversion en points par euro
  } catch (error) {
    console.error("Error fetching conversion rate:", error);
    return 100; // Valeur par défaut : 100 points par euro
  }
}

/**
 * Récupérer tous les produits de la boutique Shopify
 */
export async function getShopifyProducts(request: Request): Promise<ShopifyProduct[]> {
  try {
    const { admin, session } = await authenticate.admin(request);
    const shop = session.shop;

    // Récupérer le taux de conversion
    const pointsPerEuro = await getPointsConversionRate(shop);

    // Requête GraphQL pour récupérer les produits
    const response = await admin.graphql(`
      query getProducts($first: Int!) {
        products(first: $first) {
          edges {
            node {
              id
              title
              handle
              featuredImage {
                url
                altText
              }
              variants(first: 1) {
                edges {
                  node {
                    id
                    title
                    price
                  }
                }
              }
            }
          }
        }
      }
    `, {
      variables: {
        first: 50 // Récupérer les 50 premiers produits
      }
    });

    const data = await response.json();

    if (!data.data?.products?.edges) {
      return [];
    }

    // Transformer les données Shopify en format utilisable
    const products: ShopifyProduct[] = data.data.products.edges.map((edge: any) => {
      const product = edge.node;
      const variant = product.variants.edges[0]?.node;

      return {
        id: product.id,
        title: product.title,
        handle: product.handle,
        image: product.featuredImage?.url,
        price: variant ? `${parseFloat(variant.price).toFixed(2)}€` : '0.00€',
        pointsCost: variant ? Math.round(parseFloat(variant.price) * pointsPerEuro) : 0, // Utilise le taux de conversion dynamique
        variants: product.variants.edges.map((variantEdge: any) => ({
          id: variantEdge.node.id,
          price: variantEdge.node.price,
          title: variantEdge.node.title
        }))
      };
    });

    return products;
  } catch (error) {
    console.error("Error fetching Shopify products:", error);
    return [];
  }
}

/**
 * Rechercher des produits par titre
 */
export async function searchShopifyProducts(request: Request, query: string): Promise<ShopifyProduct[]> {
  try {
    const { admin, session } = await authenticate.admin(request);
    const shop = session.shop;

    // Récupérer le taux de conversion
    const pointsPerEuro = await getPointsConversionRate(shop);

    const response = await admin.graphql(`
      query searchProducts($query: String!, $first: Int!) {
        products(first: $first, query: $query) {
          edges {
            node {
              id
              title
              handle
              featuredImage {
                url
                altText
              }
              variants(first: 1) {
                edges {
                  node {
                    id
                    title
                    price
                  }
                }
              }
            }
          }
        }
      }
    `, {
      variables: {
        query: `title:*${query}*`,
        first: 20
      }
    });

    const data = await response.json();

    if (!data.data?.products?.edges) {
      return [];
    }

    const products: ShopifyProduct[] = data.data.products.edges.map((edge: any) => {
      const product = edge.node;
      const variant = product.variants.edges[0]?.node;

      return {
        id: product.id,
        title: product.title,
        handle: product.handle,
        image: product.featuredImage?.url,
        price: variant ? `${parseFloat(variant.price).toFixed(2)}€` : '0.00€',
        pointsCost: variant ? Math.round(parseFloat(variant.price) * pointsPerEuro) : 0,
        variants: product.variants.edges.map((variantEdge: any) => ({
          id: variantEdge.node.id,
          price: variantEdge.node.price,
          title: variantEdge.node.title
        }))
      };
    });

    return products;
  } catch (error) {
    console.error("Error searching Shopify products:", error);
    return [];
  }
}
