/* ===== VARIABLES CSS ===== */
:root {
  --loyalty-primary: #2E7D32;
  --loyalty-secondary: #4CAF50;
  --loyalty-accent: #81C784;
  --loyalty-background: #ffffff; /* Background du panel principal */
  --loyalty-widget-background: #ffffff; /* Background spécifique du widget (contrôlé par admin) */
  --loyalty-surface: #f8f9fa;
  --loyalty-text: #212121;
  --loyalty-text-secondary: #757575;
  --loyalty-text-color: #ffffff; /* Couleur du texte dans le widget */
  --loyalty-border: #e0e0e0;
  --loyalty-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  --loyalty-shadow-hover: 0 8px 30px rgba(0, 0, 0, 0.15);
  --loyalty-radius: 16px;
  --loyalty-radius-small: 8px;
  --loyalty-size-multiplier: 1; /* Multiplicateur de taille */
  --loyalty-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --loyalty-z-index: 999999;
}

/* ===== RESET ET BASE ===== */
.loyalty-widget-container * {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

.loyalty-widget-container {
  position: fixed;
  bottom: 24px;
  right: 24px;
  z-index: var(--loyalty-z-index);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  font-size: 14px;
  line-height: 1.5;
  color: var(--loyalty-text);
}

/* ===== BOUTON FLOTTANT PRINCIPAL ===== */
.loyalty-trigger {
  position: relative;
  width: calc(64px * var(--loyalty-size-multiplier));
  height: calc(64px * var(--loyalty-size-multiplier));
  background: linear-gradient(135deg, var(--loyalty-primary) 0%, var(--loyalty-secondary) 100%);
  border-radius: 50%;
  cursor: pointer;
  box-shadow: var(--loyalty-shadow);
  transition: var(--loyalty-transition);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.loyalty-trigger:hover {
  transform: translateY(-2px) scale(1.05);
  box-shadow: var(--loyalty-shadow-hover);
}

.loyalty-trigger-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--loyalty-text-color);
  z-index: 2;
  position: relative;
}

.loyalty-icon {
  margin-bottom: 2px;
  transition: var(--loyalty-transition);
}

.loyalty-points-preview {
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: 10px;
  font-weight: 600;
  line-height: 1;
}

.points-count {
  font-size: 12px;
  font-weight: 700;
}

.points-label {
  font-size: 8px;
  opacity: 0.9;
}

/* Animation de pulsation */
.loyalty-pulse {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: var(--loyalty-secondary);
  opacity: 0;
  animation: loyalty-pulse 2s infinite;
}

@keyframes loyalty-pulse {
  0% {
    transform: scale(1);
    opacity: 0.7;
  }
  70% {
    transform: scale(1.4);
    opacity: 0;
  }
  100% {
    transform: scale(1.4);
    opacity: 0;
  }
}

/* ===== PANEL PRINCIPAL ===== */
.loyalty-panel {
  position: absolute;
  bottom: 80px;
  right: 0;
  width: 380px;
  max-height: 600px;
  background: var(--loyalty-widget-background);
  border-radius: var(--loyalty-radius);
  box-shadow: var(--loyalty-shadow-hover);
  opacity: 0;
  visibility: hidden;
  transform: translateY(20px) scale(0.95);
  transition: var(--loyalty-transition);
  overflow: hidden;
  border: 1px solid var(--loyalty-border);
}

.loyalty-panel.active {
  opacity: 1;
  visibility: visible;
  transform: translateY(0) scale(1);
}

.loyalty-panel-header {
  background: linear-gradient(135deg, var(--loyalty-primary) 0%, var(--loyalty-secondary) 100%);
  color: var(--loyalty-text-color);
  padding: 20px;
  position: relative;
}

.loyalty-header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.loyalty-title {
  font-size: 18px;
  font-weight: 600;
  margin: 0;
  color: var(--loyalty-text-color);
}

.loyalty-close-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: white;
  transition: var(--loyalty-transition);
}

.loyalty-close-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.loyalty-panel-body {
  padding: 24px;
  max-height: 520px;
  overflow-y: auto;
}

/* ===== ÉTATS DU WIDGET ===== */

/* État de chargement */
.loyalty-loading {
  text-align: center;
  padding: 40px 20px;
}

.loyalty-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--loyalty-surface);
  border-top: 3px solid var(--loyalty-primary);
  border-radius: 50%;
  animation: loyalty-spin 1s linear infinite;
  margin: 0 auto 16px;
}

@keyframes loyalty-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* État invité */
.loyalty-guest {
  text-align: center;
  padding: 20px 0;
}

.loyalty-guest-icon {
  color: var(--loyalty-primary);
  margin-bottom: 16px;
}

.loyalty-guest h4 {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 8px;
  color: var(--loyalty-text);
}

.loyalty-guest p {
  color: var(--loyalty-text-secondary);
  margin-bottom: 24px;
  line-height: 1.6;
}

/* État membre */
.loyalty-member {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* Informations client */
.loyalty-customer-info {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: var(--loyalty-surface);
  border-radius: var(--loyalty-radius-small);
}

.loyalty-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--loyalty-primary) 0%, var(--loyalty-secondary) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 18px;
}

.loyalty-customer-details h4 {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 4px;
}

.loyalty-status-badge {
  display: inline-block;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.loyalty-status-badge.member {
  background: #e8f5e8;
  color: var(--loyalty-primary);
}

.loyalty-status-badge.guest {
  background: #e3f2fd;
  color: #1976d2;
}

/* Statistiques */
.loyalty-stats {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.loyalty-stat-card {
  background: var(--loyalty-surface);
  border-radius: var(--loyalty-radius-small);
  padding: 10px;
  display: flex;
  align-items: center;
  gap: 12px;
  transition: var(--loyalty-transition);
}

.loyalty-stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.loyalty-stat-primary {
  background: linear-gradient(135deg, var(--loyalty-primary) 0%, var(--loyalty-secondary) 100%);
  color: white;
}

.loyalty-stat-icon {
  opacity: 0.8;
}

.loyalty-stat-content {
  display: flex;
  flex-direction: column;
}

.loyalty-stat-value {
  font-size: 20px;
  font-weight: 700;
  line-height: 1;
}

.loyalty-stat-label {
  font-size: 12px;
  opacity: 0.8;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Barre de progression */
.loyalty-progress {
  background: var(--loyalty-surface);
  border-radius: var(--loyalty-radius-small);
  padding: 20px;
}

.loyalty-progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.loyalty-progress-title {
  font-weight: 600;
  color: var(--loyalty-text);
}

.loyalty-progress-remaining {
  font-size: 12px;
  color: var(--loyalty-primary);
  font-weight: 600;
}

.loyalty-progress-bar {
  height: 8px;
  background: #e0e0e0;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 12px;
}

.loyalty-progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--loyalty-primary) 0%, var(--loyalty-secondary) 100%);
  border-radius: 4px;
  transition: width 0.6s ease;
}

.loyalty-progress-text {
  font-size: 12px;
  color: var(--loyalty-text-secondary);
  margin: 0;
}

/* Actions */
.loyalty-actions {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

/* Boutons */
.loyalty-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 20px;
  border-radius: var(--loyalty-radius-small);
  font-size: 14px;
  font-weight: 600;
  text-decoration: none;
  border: none;
  cursor: pointer;
  transition: var(--loyalty-transition);
  text-align: center;
}

.loyalty-btn-primary {
  background: linear-gradient(135deg, var(--loyalty-primary) 0%, var(--loyalty-secondary) 100%);
  color: white;
}

.loyalty-btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(46, 125, 50, 0.3);
}

.loyalty-btn-outline {
  background: transparent;
  color: var(--loyalty-primary);
  border: 2px solid var(--loyalty-primary);
}

.loyalty-btn-outline:hover {
  background: var(--loyalty-primary);
  color: white;
  transform: translateY(-1px);
}

/* Section Récompenses et Coupons */
.loyalty-rewards-section,
.loyalty-coupon-config-section,
.loyalty-coupon-result-section {
  margin-top: 24px;
}

.loyalty-rewards-section h5,
.loyalty-coupon-config-section h5,
.loyalty-coupon-result-section h5 {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 16px;
  color: var(--loyalty-text);
}

.loyalty-rewards-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-height: 300px;
  overflow-y: auto;
}

.loyalty-reward-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: var(--loyalty-surface);
  border-radius: var(--loyalty-radius-small);
  transition: var(--loyalty-transition);
  cursor: pointer;
  border: 2px solid transparent;
}

.loyalty-reward-item:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-1px);
}

.loyalty-reward-item.unavailable {
  opacity: 0.6;
  cursor: not-allowed;
}

.loyalty-reward-item.unavailable:hover {
  transform: none;
}

.loyalty-reward-image {
  width: 48px;
  height: 48px;
  border-radius: var(--loyalty-radius-small);
  object-fit: cover;
  flex-shrink: 0;
  background: #f0f0f0;
}

.loyalty-reward-content {
  flex: 1;
}

.loyalty-reward-title {
  font-weight: 600;
  color: var(--loyalty-text);
  margin-bottom: 4px;
  font-size: 14px;
}

.loyalty-reward-cost {
  font-size: 14px;
  font-weight: 600;
  color: var(--loyalty-primary);
}

.loyalty-reward-action {
  flex-shrink: 0;
}

.loyalty-reward-btn {
  background: linear-gradient(135deg, var(--loyalty-primary), var(--loyalty-secondary));
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: var(--loyalty-radius-small);
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: var(--loyalty-transition);
}

.loyalty-reward-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(46, 125, 50, 0.3);
}

.loyalty-reward-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Façons de gagner */
.loyalty-earn-ways h5 {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 16px;
  color: var(--loyalty-text);
}

.loyalty-earn-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.loyalty-earn-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: var(--loyalty-surface);
  border-radius: var(--loyalty-radius-small);
  border-left: 4px solid var(--loyalty-primary);
}

.loyalty-earn-icon {
  color: var(--loyalty-primary);
  flex-shrink: 0;
}

.loyalty-earn-content {
  flex: 1;
}

.loyalty-earn-title {
  font-weight: 600;
  margin-bottom: 2px;
}

.loyalty-earn-description {
  font-size: 12px;
  color: var(--loyalty-text-secondary);
}

/* État d'erreur */
.loyalty-error {
  text-align: center;
  padding: 40px 20px;
}

.loyalty-error-icon {
  color: #f44336;
  margin-bottom: 16px;
}

.loyalty-error h4 {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px;
  color: var(--loyalty-text);
}

.loyalty-error p {
  color: var(--loyalty-text-secondary);
  margin-bottom: 24px;
}

/* Overlay */
.loyalty-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  opacity: 0;
  visibility: hidden;
  transition: var(--loyalty-transition);
  z-index: calc(var(--loyalty-z-index) - 1);
}

.loyalty-overlay.active {
  opacity: 1;
  visibility: visible;
}

/* ===== RESPONSIVE ===== */
@media (max-width: 768px) {
  .loyalty-widget-container {
    bottom: 16px;
    right: 16px;
  }

  .loyalty-panel {
    width: calc(100vw - 32px);
    max-width: 360px;
    bottom: 80px;
    right: 0;
  }

  .loyalty-stats {
    grid-template-columns: 1fr;
  }

  .loyalty-actions {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .loyalty-panel {
    width: calc(100vw - 16px);
    right: -8px;
  }

  .loyalty-panel-body {
    padding: 16px;
  }
}

/* ===== POSITIONS ALTERNATIVES ===== */
.loyalty-widget-container.position-bottom-left {
  bottom: 24px;
  left: 24px;
  right: auto;
}

.loyalty-widget-container.position-bottom-left .loyalty-panel {
  left: 0;
  right: auto;
}

.loyalty-widget-container.position-top-right {
  top: 24px;
  bottom: auto;
  right: 24px;
}

.loyalty-widget-container.position-top-right .loyalty-panel {
  top: 80px;
  bottom: auto;
}

.loyalty-widget-container.position-top-left {
  top: 24px;
  bottom: auto;
  left: 24px;
  right: auto;
}

.loyalty-widget-container.position-top-left .loyalty-panel {
  top: 80px;
  bottom: auto;
  left: 0;
  right: auto;
}

/* ===== ANIMATIONS D'ENTRÉE ===== */
@keyframes loyalty-fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.loyalty-widget-container.animate-in {
  animation: loyalty-fadeInUp 0.6s ease-out;
}

/* ===== THÈME SOMBRE (DÉSACTIVÉ - CONTRÔLÉ PAR L'ADMIN) ===== */
/* Le thème est maintenant contrôlé par les paramètres admin, pas par les préférences système */
/*
@media (prefers-color-scheme: dark) {
  :root {
    --loyalty-background: #1e1e1e;
    --loyalty-surface: #2d2d2d;
    --loyalty-text: #ffffff;
    --loyalty-text-secondary: #b0b0b0;
    --loyalty-border: #404040;
  }
}
*/

/* Actions */
.loyalty-actions {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

/* Boutons */
.loyalty-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 20px;
  border-radius: var(--loyalty-radius-small);
  font-size: 14px;
  font-weight: 600;
  text-decoration: none;
  border: none;
  cursor: pointer;
  transition: var(--loyalty-transition);
  text-align: center;
}

.loyalty-btn-primary {
  background: linear-gradient(135deg, var(--loyalty-primary) 0%, var(--loyalty-secondary) 100%);
  color: white;
}

.loyalty-btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(46, 125, 50, 0.3);
}

.loyalty-btn-outline {
  background: transparent;
  color: var(--loyalty-primary);
  border: 2px solid var(--loyalty-primary);
}

.loyalty-btn-outline:hover {
  background: var(--loyalty-primary);
  color: white;
  transform: translateY(-1px);
}

/* Façons de gagner */
.loyalty-earn-ways h5 {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 16px;
  color: var(--loyalty-text);
}

.loyalty-earn-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.loyalty-earn-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: var(--loyalty-surface);
  border-radius: var(--loyalty-radius-small);
  border-left: 4px solid var(--loyalty-primary);
}

.loyalty-earn-icon {
  color: var(--loyalty-primary);
  flex-shrink: 0;
}

.loyalty-earn-content {
  flex: 1;
}

.loyalty-earn-title {
  font-weight: 600;
  margin-bottom: 2px;
}

.loyalty-earn-description {
  font-size: 12px;
  color: var(--loyalty-text-secondary);
}

/* État d'erreur */
.loyalty-error {
  text-align: center;
  padding: 40px 20px;
}

.loyalty-error-icon {
  color: #f44336;
  margin-bottom: 16px;
}

.loyalty-error h4 {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px;
  color: var(--loyalty-text);
}

.loyalty-error p {
  color: var(--loyalty-text-secondary);
  margin-bottom: 24px;
}

/* Overlay */
.loyalty-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  opacity: 0;
  visibility: hidden;
  transition: var(--loyalty-transition);
  z-index: calc(var(--loyalty-z-index) - 1);
}

.loyalty-overlay.active {
  opacity: 1;
  visibility: visible;
}

/* ===== RESPONSIVE ===== */
@media (max-width: 768px) {
  .loyalty-widget-container {
    bottom: 16px;
    right: 16px;
  }

  .loyalty-panel {
    width: calc(100vw - 32px);
    max-width: 360px;
    bottom: 80px;
    right: 0;
  }

  .loyalty-stats {
    grid-template-columns: 1fr;
  }

  .loyalty-actions {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .loyalty-panel {
    width: calc(100vw - 16px);
    right: -8px;
  }

  .loyalty-panel-body {
    padding: 16px;
  }
}

/* ===== POSITIONS ALTERNATIVES ===== */
.loyalty-widget-container.position-bottom-left {
  bottom: 24px;
  left: 24px;
  right: auto;
}

.loyalty-widget-container.position-bottom-left .loyalty-panel {
  left: 0;
  right: auto;
}

.loyalty-widget-container.position-top-right {
  top: 24px;
  bottom: auto;
  right: 24px;
}

.loyalty-widget-container.position-top-right .loyalty-panel {
  top: 80px;
  bottom: auto;
}

.loyalty-widget-container.position-top-left {
  top: 24px;
  bottom: auto;
  left: 24px;
  right: auto;
}

.loyalty-widget-container.position-top-left .loyalty-panel {
  top: 80px;
  bottom: auto;
  left: 0;
  right: auto;
}

/* ===== ANIMATIONS D'ENTRÉE ===== */
@keyframes loyalty-fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.loyalty-widget-container.animate-in {
  animation: loyalty-fadeInUp 0.6s ease-out;
}

/* ===== THÈME SOMBRE (DÉSACTIVÉ - CONTRÔLÉ PAR L'ADMIN) ===== */
/* Le thème est maintenant contrôlé par les paramètres admin, pas par les préférences système */
/*
@media (prefers-color-scheme: dark) {
  :root {
    --loyalty-background: #1e1e1e;
    --loyalty-surface: #2d2d2d;
    --loyalty-text: #ffffff;
    --loyalty-text-secondary: #b0b0b0;
    --loyalty-border: #404040;
  }
}
*/

/* ===== VUES SECONDAIRES ===== */
.loyalty-rewards-content,
.loyalty-history-content {
  display: none;
}

.loyalty-view-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.loyalty-back-btn {
  background: rgba(255, 255, 255, 0.1);
  border: none;
  border-radius: 8px;
  padding: 8px 12px;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  transition: all 0.2s ease;
}

.loyalty-back-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateX(-2px);
}

.loyalty-view-header h3 {
  margin: 0;
  color: white;
  font-size: 18px;
  font-weight: 600;
}

/* Liste des récompenses */
.loyalty-rewards-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.loyalty-reward-item {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  transition: all 0.2s ease;
}

.loyalty-reward-item:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-1px);
}

.loyalty-reward-item.unavailable {
  opacity: 0.6;
}

.loyalty-reward-icon {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, var(--loyalty-primary), var(--loyalty-secondary));
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
}

.loyalty-reward-content {
  flex: 1;
}

.loyalty-reward-title {
  font-weight: 600;
  color: white;
  margin-bottom: 4px;
}

.loyalty-reward-description {
  font-size: 13px;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 6px;
}

.loyalty-reward-cost {
  font-size: 14px;
  font-weight: 600;
  color: var(--loyalty-secondary);
}

.loyalty-reward-action {
  flex-shrink: 0;
}

.loyalty-points-needed {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
  font-style: italic;
}

.loyalty-btn-sm {
  padding: 8px 16px;
  font-size: 12px;
}

/* Liste de l'historique */
.loyalty-history-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.loyalty-history-item {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.loyalty-history-icon {
  width: 36px;
  height: 36px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.loyalty-history-icon.positive {
  background: rgba(76, 175, 80, 0.2);
  color: #4CAF50;
}

.loyalty-history-icon.negative {
  background: rgba(244, 67, 54, 0.2);
  color: #f44336;
}

.loyalty-history-content {
  flex: 1;
}

.loyalty-history-description {
  font-weight: 500;
  color: white;
  margin-bottom: 4px;
}

.loyalty-history-date {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
}

.loyalty-history-points {
  font-weight: 600;
  font-size: 16px;
  flex-shrink: 0;
}

.loyalty-history-points.positive {
  color: #4CAF50;
}

.loyalty-history-points.negative {
  color: #f44336;
}

/* État vide */
.loyalty-empty-state {
  text-align: center;
  padding: 40px 20px;
  color: rgba(255, 255, 255, 0.6);
  font-style: italic;
}

/* ===== SECTION PARRAINAGE ===== */
.loyalty-referral-section {
  margin-top: 20px;
}

.loyalty-referral-card {
  background: var(--loyalty-surface);
  border-radius: var(--loyalty-radius-small);
  padding: 20px;
  border: 1px solid var(--loyalty-border);
}

.loyalty-referral-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.loyalty-referral-header h6 {
  font-size: 16px;
  font-weight: 600;
  color: var(--loyalty-text);
  margin: 0;
}

.loyalty-referral-count {
  font-size: 12px;
  color: var(--loyalty-text-secondary);
  background: rgba(46, 125, 50, 0.1);
  padding: 4px 8px;
  border-radius: 12px;
}

.loyalty-referral-description {
  font-size: 14px;
  color: var(--loyalty-text-secondary);
  margin-bottom: 16px;
  line-height: 1.4;
}

.loyalty-referral-link-container {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
}

.loyalty-referral-input {
  flex: 1;
  padding: 12px;
  border: 1px solid var(--loyalty-border);
  border-radius: var(--loyalty-radius-small);
  font-size: 14px;
  background: white;
  color: var(--loyalty-text);
}

.loyalty-copy-btn {
  padding: 12px;
  background: var(--loyalty-primary);
  color: white;
  border: none;
  border-radius: var(--loyalty-radius-small);
  cursor: pointer;
  transition: var(--loyalty-transition);
  display: flex;
  align-items: center;
  justify-content: center;
}

.loyalty-copy-btn:hover {
  background: #1b5e20;
  transform: translateY(-1px);
}

.loyalty-social-share {
  display: flex;
  gap: 8px;
}

.loyalty-social-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  padding: 10px 12px;
  border: none;
  border-radius: var(--loyalty-radius-small);
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: var(--loyalty-transition);
}

.loyalty-facebook-btn {
  background: #1877f2;
  color: white;
}

.loyalty-facebook-btn:hover {
  background: #166fe5;
  transform: translateY(-1px);
}

.loyalty-twitter-btn {
  background: #1da1f2;
  color: white;
}

.loyalty-twitter-btn:hover {
  background: #0d8bd9;
  transform: translateY(-1px);
}

.loyalty-email-btn {
  background: #34495e;
  color: white;
}

.loyalty-email-btn:hover {
  background: #2c3e50;
  transform: translateY(-1px);
}

/* ===== ANIMATIONS POUR LES SECTIONS ===== */
.loyalty-section-slide-in {
  animation: slideInFromBottom 0.3s ease-out;
}

@keyframes slideInFromBottom {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* ===== STYLES POUR LES BOUTONS DE RETOUR ===== */
.loyalty-section-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.loyalty-back-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background: var(--loyalty-surface);
  border: 1px solid var(--loyalty-border);
  border-radius: var(--loyalty-radius-small);
  color: var(--loyalty-text);
  font-size: 14px;
  cursor: pointer;
  transition: var(--loyalty-transition);
}

.loyalty-back-btn:hover {
  background: var(--loyalty-border);
  transform: translateX(-2px);
}

.loyalty-back-icon {
  font-size: 16px;
}

/* ===== STYLES POUR L'HISTORIQUE ===== */
.loyalty-history-list {
  display: grid;
  gap: 8px;
  max-height: 300px;
  overflow-y: auto;
}

.loyalty-history-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: var(--loyalty-surface);
  border-radius: var(--loyalty-radius-small);
  border: 1px solid var(--loyalty-border);
}

.loyalty-history-info {
  flex: 1;
}

.loyalty-history-action {
  font-size: 14px;
  font-weight: 500;
  color: var(--loyalty-text);
  margin: 0 0 4px 0;
}

.loyalty-history-date {
  font-size: 12px;
  color: var(--loyalty-text-secondary);
}

.loyalty-history-points {
  font-size: 14px;
  font-weight: 600;
}

.loyalty-history-points.positive {
  color: #2e7d32;
}

.loyalty-history-points.negative {
  color: #d32f2f;
}

/* ===== SECTIONS COUPON ===== */

/* Configuration du coupon */
.loyalty-coupon-config {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.loyalty-coupon-preview {
  text-align: center;
  padding: 24px;
  background: var(--loyalty-surface);
  border-radius: var(--loyalty-radius-small);
}

.loyalty-coupon-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.loyalty-coupon-preview h6 {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 8px;
  color: var(--loyalty-text);
}

.loyalty-coupon-preview p {
  color: var(--loyalty-text-secondary);
  font-size: 14px;
}

.loyalty-coupon-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.loyalty-coupon-form label {
  font-weight: 600;
  color: var(--loyalty-text);
  margin-bottom: 8px;
}

.loyalty-points-input {
  padding: 12px 16px;
  border: 2px solid var(--loyalty-border);
  border-radius: var(--loyalty-radius-small);
  font-size: 16px;
  font-weight: 600;
  text-align: center;
  background: var(--loyalty-surface);
  color: var(--loyalty-text);
  transition: var(--loyalty-transition);
}

.loyalty-points-input:focus {
  outline: none;
  border-color: var(--loyalty-primary);
  box-shadow: 0 0 0 3px rgba(46, 125, 50, 0.1);
}

.loyalty-coupon-value {
  text-align: center;
  padding: 16px;
  background: linear-gradient(135deg, var(--loyalty-primary) 0%, var(--loyalty-secondary) 100%);
  color: white;
  border-radius: var(--loyalty-radius-small);
  font-size: 18px;
}

.loyalty-coupon-value strong {
  font-size: 24px;
  font-weight: 700;
}

/* Coupon généré */
.loyalty-coupon-result {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.loyalty-coupon-generated {
  text-align: center;
  padding: 24px;
  background: var(--loyalty-surface);
  border-radius: var(--loyalty-radius-small);
}

.loyalty-coupon-generated h6 {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 8px;
  color: var(--loyalty-text);
}

.loyalty-coupon-generated p {
  color: var(--loyalty-text-secondary);
  font-size: 14px;
  margin-bottom: 16px;
}

.loyalty-coupon-description {
  margin-bottom: 24px !important;
}

.loyalty-coupon-code-container {
  display: flex;
  gap: 8px;
  margin-bottom: 24px;
  align-items: center;
}

.loyalty-coupon-code {
  flex: 1;
  padding: 16px;
  border: 2px solid var(--loyalty-primary);
  border-radius: var(--loyalty-radius-small);
  font-size: 18px;
  font-weight: 700;
  text-align: center;
  background: rgba(46, 125, 50, 0.05);
  color: var(--loyalty-primary);
  letter-spacing: 2px;
}

.loyalty-copy-btn {
  background: var(--loyalty-primary);
  color: white;
  border: none;
  border-radius: var(--loyalty-radius-small);
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--loyalty-transition);
}

.loyalty-copy-btn:hover {
  background: var(--loyalty-secondary);
  transform: scale(1.05);
}

/* Header avec points */
.loyalty-section-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--loyalty-border);
}

.loyalty-back-btn {
  background: none;
  border: none;
  color: var(--loyalty-primary);
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: var(--loyalty-transition);
}

.loyalty-back-btn:hover {
  color: var(--loyalty-secondary);
}

.loyalty-back-icon {
  font-size: 20px;
}

/* ===== NOUVELLES SECTIONS DE NAVIGATION ===== */

/* Cartes de navigation principales */
.loyalty-nav-card {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: var(--loyalty-surface);
  border-radius: var(--loyalty-radius-small);
  margin-bottom: 12px;
  cursor: pointer;
  transition: var(--loyalty-transition);
  border: 2px solid transparent;
}

.loyalty-nav-card:hover {
  background: rgba(46, 125, 50, 0.05);
  transform: translateY(-1px);
  border-color: var(--loyalty-primary);
}

.loyalty-nav-card-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--loyalty-primary) 0%, var(--loyalty-secondary) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
}

.loyalty-nav-card-content {
  flex: 1;
}

.loyalty-nav-card-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--loyalty-text);
  margin-bottom: 4px;
}

.loyalty-nav-card-subtitle {
  font-size: 14px;
  color: var(--loyalty-text-secondary);
}

.loyalty-nav-card-arrow {
  color: var(--loyalty-text-secondary);
  transition: var(--loyalty-transition);
}

.loyalty-nav-card:hover .loyalty-nav-card-arrow {
  color: var(--loyalty-primary);
  transform: translateX(4px);
}

/* Sections dédiées */
.loyalty-your-rewards-section,
.loyalty-ways-to-earn-section,
.loyalty-ways-to-redeem-section,
.loyalty-your-activity-section {
  display: none;
}

.loyalty-section-content {
  padding: 0;
}

.loyalty-section-content h5 {
  font-size: 20px;
  font-weight: 700;
  margin-bottom: 20px;
  color: var(--loyalty-text);
}

/* Your Rewards Section */
.loyalty-your-rewards-list,
.loyalty-past-rewards-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 24px;
}

.loyalty-past-rewards h6 {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 16px;
  color: var(--loyalty-text);
}

.loyalty-reward-item.past {
  opacity: 0.7;
}

.loyalty-reward-subtitle {
  font-size: 12px;
  color: var(--loyalty-text-secondary);
  margin-top: 4px;
}

.loyalty-reward-status {
  color: var(--loyalty-primary);
}

/* Ways to Earn Section */
.loyalty-ways-to-earn-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.loyalty-earn-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: var(--loyalty-surface);
  border-radius: var(--loyalty-radius-small);
}

.loyalty-earn-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--loyalty-primary) 0%, var(--loyalty-secondary) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
}

.loyalty-earn-content {
  flex: 1;
}

.loyalty-earn-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--loyalty-text);
  margin-bottom: 4px;
}

.loyalty-earn-description {
  font-size: 14px;
  color: var(--loyalty-text-secondary);
}

/* Ways to Redeem Section */
.loyalty-ways-to-redeem-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.loyalty-redeem-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: var(--loyalty-surface);
  border-radius: var(--loyalty-radius-small);
  transition: var(--loyalty-transition);
}

.loyalty-redeem-item.unavailable {
  opacity: 0.6;
}

.loyalty-redeem-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--loyalty-primary) 0%, var(--loyalty-secondary) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
}

.loyalty-redeem-content {
  flex: 1;
}

.loyalty-redeem-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--loyalty-text);
  margin-bottom: 4px;
}

.loyalty-redeem-description {
  font-size: 14px;
  color: var(--loyalty-text-secondary);
}

.loyalty-redeem-action {
  flex-shrink: 0;
}

.loyalty-redeem-btn {
  padding: 8px 16px;
  background: var(--loyalty-primary);
  color: white;
  border: none;
  border-radius: var(--loyalty-radius-small);
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: var(--loyalty-transition);
  min-width: 80px;
}

.loyalty-redeem-btn:hover {
  background: var(--loyalty-secondary);
}

.loyalty-redeem-btn.disabled,
.loyalty-redeem-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.loyalty-redeem-btn.disabled:hover,
.loyalty-redeem-btn:disabled:hover {
  background: #ccc;
}

/* Your Activity Section */
.loyalty-activity-tabs {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
  background: var(--loyalty-surface);
  border-radius: var(--loyalty-radius-small);
  padding: 4px;
}

.loyalty-tab-btn {
  flex: 1;
  padding: 12px 16px;
  background: none;
  border: none;
  border-radius: var(--loyalty-radius-small);
  font-size: 14px;
  font-weight: 600;
  color: var(--loyalty-text-secondary);
  cursor: pointer;
  transition: var(--loyalty-transition);
}

.loyalty-tab-btn.active {
  background: var(--loyalty-primary);
  color: white;
}

.loyalty-activity-notice {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: var(--loyalty-radius-small);
  padding: 12px 16px;
  margin-bottom: 20px;
}

.loyalty-activity-notice p {
  font-size: 13px;
  color: #856404;
  margin: 0;
}

.loyalty-activity-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.loyalty-activity-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: var(--loyalty-surface);
  border-radius: var(--loyalty-radius-small);
}

.loyalty-activity-content {
  flex: 1;
}

.loyalty-activity-description {
  font-size: 14px;
  color: var(--loyalty-text);
  margin-bottom: 4px;
}

.loyalty-activity-points {
  font-size: 14px;
  font-weight: 600;
}

.loyalty-activity-points.earned {
  color: #4CAF50;
}

.loyalty-activity-points.spent {
  color: #f44336;
}

.loyalty-activity-date {
  font-size: 12px;
  color: var(--loyalty-text-secondary);
}

/* Ways to Redeem intégrée dans le layout principal */
.loyalty-ways-to-redeem-main {
  margin: 20px 0;
}

.loyalty-section-title {
  margin-bottom: 16px;
}

.loyalty-section-title h6 {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 4px 0;
  color: var(--loyalty-text);
}

.loyalty-section-subtitle {
  font-size: 13px;
  color: var(--loyalty-text-secondary);
}

.loyalty-redeem-options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 12px;
}

.loyalty-redeem-option {
  background: var(--loyalty-surface);
  border-radius: var(--loyalty-radius-small);
  padding: 16px;
  border: 2px solid transparent;
  cursor: pointer;
  transition: var(--loyalty-transition);
  position: relative;
  overflow: hidden;
}

.loyalty-redeem-option:hover {
  border-color: var(--loyalty-primary);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(46, 125, 50, 0.15);
}

.loyalty-redeem-option.unavailable {
  opacity: 0.6;
  cursor: not-allowed;
}

.loyalty-redeem-option.unavailable:hover {
  transform: none;
  border-color: transparent;
  box-shadow: none;
}

.loyalty-redeem-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.loyalty-redeem-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--loyalty-primary) 0%, var(--loyalty-secondary) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
}

.loyalty-redeem-badge {
  background: var(--loyalty-primary);
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
}

.loyalty-redeem-badge.unavailable {
  background: #f44336;
}

.loyalty-redeem-content h7 {
  font-size: 15px;
  font-weight: 600;
  color: var(--loyalty-text);
  margin: 0 0 6px 0;
  display: block;
}

.loyalty-redeem-description {
  font-size: 13px;
  color: var(--loyalty-text-secondary);
  margin-bottom: 12px;
  line-height: 1.4;
}

.loyalty-redeem-cost {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: 12px;
  border-top: 1px solid var(--loyalty-border);
}

.loyalty-redeem-points {
  font-size: 14px;
  font-weight: 600;
  color: var(--loyalty-primary);
}

.loyalty-redeem-value {
  font-size: 13px;
  color: var(--loyalty-text-secondary);
}

.loyalty-redeem-action {
  margin-top: 12px;
}

.loyalty-redeem-btn {
  width: 100%;
  padding: 10px 16px;
  background: var(--loyalty-primary);
  color: white;
  border: none;
  border-radius: var(--loyalty-radius-small);
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: var(--loyalty-transition);
}

.loyalty-redeem-btn:hover {
  background: var(--loyalty-secondary);
}

.loyalty-redeem-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.loyalty-redeem-btn:disabled:hover {
  background: #ccc;
}

/* Animations de transition */
.loyalty-section-slide-in {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* ===== MODAL DE SUCCÈS D'ÉCHANGE ===== */
.loyalty-redemption-success-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.loyalty-redemption-success-modal.active {
  opacity: 1;
  visibility: visible;
}

.loyalty-modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.loyalty-modal-content {
  background: white;
  border-radius: 12px;
  max-width: 400px;
  width: 100%;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  transform: scale(0.9);
  transition: transform 0.3s ease;
}

.loyalty-redemption-success-modal.active .loyalty-modal-content {
  transform: scale(1);
}

.loyalty-success-header {
  text-align: center;
  padding: 24px 24px 16px;
  border-bottom: 1px solid #eee;
}

.loyalty-success-icon {
  font-size: 48px;
  margin-bottom: 12px;
}

.loyalty-success-header h3 {
  margin: 0;
  color: var(--loyalty-text);
  font-size: 20px;
  font-weight: 600;
}

.loyalty-success-body {
  padding: 24px;
  text-align: center;
}

.loyalty-success-body p {
  margin: 0 0 16px;
  color: var(--loyalty-text-secondary);
  line-height: 1.5;
}

.loyalty-code-display {
  background: #f8f9fa;
  border: 2px dashed var(--loyalty-primary);
  border-radius: 8px;
  padding: 16px;
  margin: 16px 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.loyalty-code {
  flex: 1;
  font-family: monospace;
  font-size: 18px;
  font-weight: bold;
  color: var(--loyalty-primary);
  letter-spacing: 1px;
}

.loyalty-copy-btn {
  background: var(--loyalty-primary);
  color: white;
  border: none;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 12px;
  cursor: pointer;
  transition: var(--loyalty-transition);
  white-space: nowrap;
}

.loyalty-copy-btn:hover {
  background: var(--loyalty-secondary);
}

.loyalty-code-instructions {
  font-size: 14px;
  color: var(--loyalty-text-secondary);
  margin-top: 16px !important;
}

.loyalty-success-footer {
  padding: 16px 24px 24px;
  display: flex;
  gap: 12px;
  justify-content: center;
}

.loyalty-btn-primary,
.loyalty-btn-secondary {
  padding: 10px 20px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: var(--loyalty-transition);
  border: none;
  flex: 1;
}

.loyalty-btn-primary {
  background: var(--loyalty-primary);
  color: white;
}

.loyalty-btn-primary:hover {
  background: var(--loyalty-secondary);
}

.loyalty-btn-secondary {
  background: transparent;
  color: var(--loyalty-primary);
  border: 2px solid var(--loyalty-primary);
}

.loyalty-btn-secondary:hover {
  background: var(--loyalty-primary);
  color: white;
}

/* ===== SECTION COUPON CONFIGURABLE ===== */
.loyalty-configurable-coupon-section {
  display: block;
}

.loyalty-coupon-config-header {
  text-align: center;
  margin-bottom: 32px;
}

.loyalty-coupon-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.loyalty-coupon-config-header h5 {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 8px;
  color: var(--loyalty-text);
}

.loyalty-coupon-rate {
  font-size: 14px;
  color: var(--loyalty-text-secondary);
  margin: 0;
}

.loyalty-coupon-calculator {
  background: var(--loyalty-surface);
  border-radius: var(--loyalty-radius-small);
  padding: 24px;
  text-align: center;
}

.loyalty-points-display {
  font-size: 20px;
  font-weight: 600;
  color: var(--loyalty-text);
  margin-bottom: 24px;
}

.loyalty-points-display #selected-points {
  color: var(--loyalty-primary);
  font-weight: 700;
}

.loyalty-points-display #coupon-value {
  color: var(--loyalty-secondary);
  font-weight: 700;
}

.loyalty-points-slider-container {
  margin: 32px 0;
}

.loyalty-points-slider {
  width: 100%;
  height: 8px;
  border-radius: 4px;
  background: #e0e0e0;
  outline: none;
  -webkit-appearance: none;
  margin-bottom: 16px;
}

.loyalty-points-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: var(--loyalty-primary);
  cursor: pointer;
  box-shadow: 0 2px 6px rgba(46, 125, 50, 0.3);
}

.loyalty-points-slider::-moz-range-thumb {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: var(--loyalty-primary);
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 6px rgba(46, 125, 50, 0.3);
}

.loyalty-slider-labels {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: var(--loyalty-text-secondary);
}

.loyalty-redeem-btn-primary {
  width: 100%;
  padding: 16px 24px;
  background: var(--loyalty-primary);
  color: white;
  border: none;
  border-radius: var(--loyalty-radius-small);
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: var(--loyalty-transition);
  margin-top: 16px;
}

.loyalty-redeem-btn-primary:hover {
  background: var(--loyalty-secondary);
  transform: translateY(-1px);
}

/* ===== MODAL DE CONFIRMATION ===== */
.loyalty-confirm-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.loyalty-confirm-modal.active {
  opacity: 1;
  visibility: visible;
}

.loyalty-confirm-header {
  text-align: center;
  padding: 24px 24px 16px;
  border-bottom: 1px solid #eee;
}

.loyalty-confirm-icon {
  font-size: 48px;
  margin-bottom: 12px;
}

.loyalty-confirm-header h3 {
  margin: 0;
  color: var(--loyalty-text);
  font-size: 20px;
  font-weight: 600;
}

.loyalty-confirm-body {
  padding: 24px;
  text-align: center;
}

.loyalty-confirm-body p {
  margin: 0 0 16px;
  color: var(--loyalty-text-secondary);
  line-height: 1.5;
}

.loyalty-confirm-footer {
  padding: 16px 24px 24px;
  display: flex;
  gap: 12px;
  justify-content: center;
}

.loyalty-loading-content {
  padding: 48px 24px;
  text-align: center;
}

.loyalty-loading-content .loyalty-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid var(--loyalty-primary);
  border-radius: 50%;
  animation: loyalty-spin 1s linear infinite;
  margin: 0 auto 16px;
}

.loyalty-loading-content p {
  color: var(--loyalty-text-secondary);
  margin: 0;
}

/* ===== SECTION RÉSULTAT COUPON ===== */
.loyalty-coupon-result-section {
  display: block;
}

.loyalty-coupon-success-header {
  text-align: center;
  margin-bottom: 32px;
}

.loyalty-coupon-success-header h5 {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 8px;
  color: var(--loyalty-text);
}

.loyalty-coupon-spent {
  font-size: 14px;
  color: var(--loyalty-text-secondary);
  margin: 0;
}

.loyalty-coupon-instructions {
  text-align: center;
  margin-bottom: 24px;
}

.loyalty-coupon-instructions p {
  color: var(--loyalty-text-secondary);
  margin: 0;
}

.loyalty-coupon-code-section {
  background: var(--loyalty-surface);
  border-radius: var(--loyalty-radius-small);
  padding: 24px;
}

.loyalty-coupon-code-display {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
}

.loyalty-coupon-code-input {
  flex: 1;
  border: 2px solid var(--loyalty-border);
  border-radius: var(--loyalty-radius-small);
  font-size: 16px;
  font-weight: 600;
  text-align: center;
  background: white;
  color: var(--loyalty-text);
  letter-spacing: 1px;
}

.loyalty-copy-code-btn {
  width: 48px;
  height: 48px;
  background: var(--loyalty-primary);
  color: white;
  border: none;
  border-radius: var(--loyalty-radius-small);
  cursor: pointer;
  transition: var(--loyalty-transition);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
}

.loyalty-copy-code-btn:hover {
  background: var(--loyalty-secondary);
}

.loyalty-apply-code-btn {
  width: 100%;
  padding: 16px 24px;
  background: var(--loyalty-primary);
  color: white;
  border: none;
  border-radius: var(--loyalty-radius-small);
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: var(--loyalty-transition);
}

.loyalty-apply-code-btn:hover {
  background: var(--loyalty-secondary);
  transform: translateY(-1px);
}

/* Responsive pour la modal */
@media (max-width: 768px) {
  .loyalty-modal-content {
    margin: 20px;
    max-width: none;
  }

  .loyalty-success-footer {
    flex-direction: column;
  }

  .loyalty-code-display {
    flex-direction: column;
    gap: 8px;
  }

  .loyalty-copy-btn {
    width: 100%;
  }

  .loyalty-coupon-code-display {
    flex-direction: column;
  }

  .loyalty-copy-code-btn {
    width: 100%;
    height: 48px;
  }

  .loyalty-reward-detail-code-display {
    flex-direction: column;
  }

  .loyalty-reward-detail-copy-btn {
    width: 100%;
    height: 48px;
  }
}

/* ===== SECTION DÉTAIL RÉCOMPENSE ===== */
.loyalty-reward-detail-section {
  display: block;
}

.loyalty-reward-detail-header {
  text-align: center;
  margin-bottom: 32px;
}

.loyalty-reward-detail-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.loyalty-reward-detail-header h5 {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 8px;
  color: var(--loyalty-text);
}

.loyalty-reward-detail-subtitle {
  font-size: 14px;
  color: var(--loyalty-text-secondary);
  margin: 0;
}

.loyalty-reward-detail-instructions {
  text-align: center;
  margin-bottom: 24px;
}

.loyalty-reward-detail-instructions p {
  color: var(--loyalty-text-secondary);
  margin: 0;
}

.loyalty-reward-detail-code-section {
  background: var(--loyalty-surface);
  border-radius: var(--loyalty-radius-small);
  padding: 24px;
}

.loyalty-reward-detail-code-display {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
}

.loyalty-reward-detail-code-input {
  flex: 1;
  border: 2px solid var(--loyalty-border);
  border-radius: var(--loyalty-radius-small);
  font-size: 16px;
  font-weight: 600;
  text-align: center;
  background: white;
  color: var(--loyalty-text);
  letter-spacing: 1px;
}

.loyalty-reward-detail-copy-btn {
  width: 48px;
  height: 48px;
  background: var(--loyalty-primary);
  color: white;
  border: none;
  border-radius: var(--loyalty-radius-small);
  cursor: pointer;
  transition: var(--loyalty-transition);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
}

.loyalty-reward-detail-copy-btn:hover {
  background: var(--loyalty-secondary);
}

.loyalty-reward-detail-apply-btn {
  width: 100%;
  padding: 16px 24px;
  background: var(--loyalty-primary);
  color: white;
  border: none;
  border-radius: var(--loyalty-radius-small);
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: var(--loyalty-transition);
}

.loyalty-reward-detail-apply-btn:hover {
  background: var(--loyalty-secondary);
  transform: translateY(-1px);
}
