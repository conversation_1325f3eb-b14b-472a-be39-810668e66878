import { ActionFunctionArgs } from "@remix-run/node";
import { authenticate } from "../shopify.server";
import { handleOrderCreated } from "../webhooks/points.server";

export const action = async ({ request }: ActionFunctionArgs) => {
  try {
    console.log("Webhook received: order-created");
    const { topic, shop, payload } = await authenticate.webhook(request);
    await handleOrderCreated(topic, shop, JSON.stringify(payload));
    return new Response(null, { status: 200 });
  } catch (error) {
    console.error("Webhook error:", error);
    return new Response(null, { status: 500 });
  }
};
